# Env Flask
FLASK_ENV="development"


# Redis Config
REDIS_URL="redis://redis-code-management:6379/0"


# Database Config
SQLALCHEMY_CONFIG_SERVICE_USER=root
SQLALCHEMY_CONFIG_SERVICE_PASSWORD=root
SQLALCHEMY_CONFIG_SERVICE_HOST=database-default
SQLALCHEMY_CONFIG_SERVICE_PORT=3306
SQLALCHEMY_CONFIG_SERVICE_DB=ura12


# Url
URL_ROUTER="http://service-router:8000"
# Service product
SERVICE_DOMAIN_A6_PRODUCT="http://storage.local"
# Service document
SERVICE_DOMAIN_A8_DOCUMENT="http://document.local"
# Service urbox
SERVICE_DOMAIN_A18_URBOX="http://get.local"

# Urbox Config
URBOX_SECRET=
URBOX_AUTHORIZATION=
#IO Media

# Third party apis

#7Eleven
7ELEVEN_HOST=https://uat-rewards.sevensystem.vn
7ELEVEN_PATH_GET_CODE=/external/v1/vouchers
7ELEVEN_TOKEN=8c6ce46fda72bb95de191a72b7abbb68f1d2607

#HoaYeuThuong
HOA_YEU_THUONG_HOST=http://mttest.hoayeuthuong.com
HOA_YEU_THUONG_PATH_GET_CODE=/UrboxService.svc/createList
HOA_YEU_THUONG_PATH_GET_ONE_CODE=/UrboxService.svc/createVoucher
HOA_YEU_THUONG_PASSWORD=URBOX_@MTTEST
HOA_YEU_THUONG_USERNAME=urbox

#IOMedia
IO_MEDIA_HOST=http://ipayservice.vn:8686
IO_MEDIA_PATH_GET_CODE=/IPayService/rest/partner/buyCard
IO_MEDIA_PATH_CHECK_BALANCE=/IPayService/rest/partner/checkBalance
IO_MEDIA_AUTHOR_CODE=test.airtime.m
IO_MEDIA_EMAIL_RECEIVE=<EMAIL>
IO_MEDIA_SECRET_CODE=urbox@170**

#JolliBee Config
JOLLIBEE_API_KEY=84e0a31c-5118-4968-94c2-0d3405bd5963
JOLLIBEE_HOST=http://************:8080
JOLLIBEE_PATH_GET_CODE=/api/EVoucher
JOLLIBEE_PATH_DELETE=/api/EVoucherDelete
JOLLIBEE_SECRET_CODE=api_user_1234
JOLLIBEE_AUTHOR_CODE=api_user

#The Coffee House Config
THE_COFFEE_HOUSE_HOST=http://sandhub.urbox.vn/ajax
THE_COFFEE_HOUSE_PATH_GET_CODE=/test/apiTestTheCoffeHouse
THE_COFFEE_HOUSE_SECRET_CODE=TEST
THE_COFFEE_HOUSE_AUTHOR_CODE=123456

#Urbox Config
URBOX_HOST="https://dev.urbox.vn"
URBOX_PATH_GET_CODE="/ajax.php?act=api&code=selectApi"
URBOX_SECRET_CODE="urbox"
URBOX_AUTHOR_CODE="urbox@170**"

#VinID Config
VIN_ID_HOST=
VIN_ID_PATH_GET_CODE=
VIN_ID_PATH_GET_ONE_CODE=
VIN_ID_API_KEY="8bdca86f-ace0-09a0-86fe-eaa8dae1f635"
VIN_ID_STORE_CODE="mJ4"
VIN_ID_POS_CODE="mJ4hW"

#VMG
VMG_HOST='http://*************:8080/ItopupService1.4/services/TopupInterface?wsdl'
VMG_USERNAME=IMEDIA_DEV16
VMG_PASSWORD=165880122185
VMG_KEY_BIRTHDAY_TIME='2021/03/02 16:51:44.663'
VMG_SOFT_PIN_KEY=701c4bdaacadca2642a42ebb

#VTC Config
VTC_PARTNER_CODE="urbox"
VTC_URL_BASE="http://**************:8080/share"
VTC_PATH_BUY_CARD="/Pay/buy-card"
VTC_BUY_CARD_CATEGORY=114
VTC_TRIPLE_DES_KEY=123456789

#EZIN
EZIN_HOST=
EZIN_PATH_BUY_CODE=
EZIN_TOKEN=

#Shopee
SHOPEE_HOST=
SHOPEE_PATH_BUY_CODE=
SHOPEE_PARTNER_CODE=
SHOPEE_PARTNER_SECRET=

#VietGuys - Default/Urbox Config
VIETGUYS_HOST=
VIETGUYS_PATH_BUY_CODE=
VIETGUYS_PATH_AUTHEN=
VIETGUYS_PARTNER_USERNAME=
VIETGUYS_ACCESS_TOKEN=
VIETGUYS_SECRET_CODE=
VIETGUYS_NETWORK_VENDOR=
#VietGuys - Jomo Config (optional, fallback to default if not set)
VIETGUYS_JOMO_HOST=
VIETGUYS_JOMO_PATH_BUY_CODE=
VIETGUYS_JOMO_PATH_AUTHEN=
VIETGUYS_JOMO_PARTNER_USERNAME=
VIETGUYS_JOMO_ACCESS_TOKEN=
VIETGUYS_JOMO_SECRET_CODE=

# Service product
SERVICE_DOMAIN_A6_PRODUCT="http://storage.urbox.local"
# Service document
SERVICE_DOMAIN_A8_DOCUMENT="http://document.urbox.local"
# Service urbox
SERVICE_DOMAIN_A18_URBOX="http://get.urbox.local"