from time import sleep

from loguru import logger

from app.config import config_app
from app.const import REDIS_SUPPLIER_EXPIRED
from app.decorators import sqlalchemy_session
from app.extensions import kafka_producer, db, redis_client

class ConsumerBeforePOOrderCode:
    @classmethod
    @sqlalchemy_session(db)
    def running(cls, value):
        logger.info("Before PO Order Code Start...")
        try:
            logger.info(value)
            cls.before_order_code(value)
        except Exception as e:
            logger.exception(e)
        return True

    @classmethod
    def before_order_code(cls, payload):
        logger.info(f"before_order_code webhook request: {payload}")
        order_entity = payload['order_entity']
        kwargs = payload['kwargs']
        quantity = kwargs.get('quantity')
        max_codes_per_call = kwargs.get('max_codes_per_call')
        supplier = payload['supplier']

        # if cls.get_po_order_code_flag(kwargs.get("request_id")):
        #     return True
        # cls.set_po_order_code_flag(kwargs.get("request_id"))
        i = 0
        while quantity > 0:
            #sleep(0.5)
            i = i+1
            quantity_need_get = quantity
            if quantity > max_codes_per_call:
                quantity_need_get = max_codes_per_call
            logger.info(f'PO: {kwargs.get("po")} - product:{supplier.get("product_id")} , quantity: {quantity_need_get}/{quantity}')
            kafka_producer.push(
                config_app.TOPIC_PO_ORDER_CODE,
                {
                    "type": "PO_ORDER_CODE",
                    "payload": {
                        "request_round": f'product-{kwargs.get("po")}-{order_entity.get("product_id")}-round-{i}',
                        "request_id": kwargs.get("request_id"),
                        "po_code": kwargs.get("po"),
                        "meta_data": kwargs.get('extra_info') or None,
                        "product": {
                            "contract_signed_with": kwargs.get("contract_signed_with"),
                            "contract_code": kwargs.get('contract_code', ""),
                            "product_id": order_entity.get("product_id"),
                            "quantity": quantity_need_get,
                            "quantity_total": quantity,
                            'price': order_entity.get("price"),
                            "product_code": order_entity.get("product_code"),
                            "product_supplier_id": order_entity.get("product_supplier_id"),
                            "product_parent_id": order_entity.get("product_parent_id"),
                            "supplier_id": order_entity.get("supplier_id"),
                            "order_id": order_entity.get("id"),
                            "supplier": supplier,
                            "expired": order_entity.get("expired"),
                            "retry_transaction_id": kwargs.get('retry_transaction_id'),
                            "product_request_id": order_entity.get("product_request_id"),
                            "scheme_code": kwargs.get("scheme_code"),
                            "effective_date": order_entity.get("effective_date"),
                        }
                    },
                },
            )
            quantity -= quantity_need_get

        return True

    @classmethod
    def get_po_order_code_flag_key(cls, request_id):
        return "FLAG_RUNNING_BEFORE_PO_ORDER_CODE" + request_id

    @classmethod
    def set_po_order_code_flag(cls, request_id):
        key = cls.get_po_order_code_flag_key(request_id)
        redis_client.set(key, 1)
        redis_client.expire(key, REDIS_SUPPLIER_EXPIRED)

    @classmethod
    def get_po_order_code_flag(cls, request_id):
        key = cls.get_po_order_code_flag_key(request_id)
        is_running = redis_client.get(key)
        if not is_running:
            return False
        return True
