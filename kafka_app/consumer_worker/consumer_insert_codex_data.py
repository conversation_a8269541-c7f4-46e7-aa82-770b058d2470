from loguru import logger

from app.decorators import sqlalchemy_session
from app.extensions import db
from app.repositories.codex_data import codex_data_repo


class ConsumerInsertCodexData:
    @classmethod
    @sqlalchemy_session(db)
    def running(cls, value):
        logger.info("Insert CodexData Start...")
        try:
            cls.insert_codex_data(value)
        except Exception as e:
            logger.exception(e)
        return True

    @classmethod
    def insert_codex_data(cls, codex_data):
        logger.info("Insert CodexData Process...")
        codex_items = codex_data['codex_items']
        entities = []
        for codex in codex_items:
            entities.append({
                "codex_id": codex.get("codex_id"),
                "partner_codex_id": codex.get("partner_codex_id") or "",
                "po_code": codex_data.get("po_code") or "",
                "request_id": codex_data.get("request_id") or "",
                "transaction_id": codex_data.get("transaction_id") or "",
                "partner_transaction_id": codex_data.get("partner_transaction_id") or "",
                "type": codex_data.get("type") or 1,
            })
        if len(entities) > 0:
            codex_data_repo.create_many(entities)
