# coding: utf8
import unittest
import time
import threading
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Mock dependencies before importing
sys.modules['loguru'] = Mock()
sys.modules['redis'] = Mock()
sys.modules['requests'] = Mock()
sys.modules['app.const'] = Mock()
sys.modules['app.config'] = Mock()
sys.modules['app.libs.base.utility'] = Mock()
sys.modules['app.libs.apis.third_party_api.abstract_api'] = Mock()
sys.modules['app.libs.apis.third_party_api.behavior_code_api'] = Mock()
sys.modules['app.security'] = Mock()

# Now import the actual modules
import redis
from app.libs.apis.third_party_api.behavior_vmg_v2_api import VMGTokenLock, LockMetrics


class TestVMGTokenLock(unittest.TestCase):
    def setUp(self):
        """Setup test fixtures"""
        self.mock_redis = Mock(spec=redis.Redis)
        self.username = "test_user"
        self.lock = VMGTokenLock(self.mock_redis, self.username)
    
    def test_init(self):
        """Test VMGTokenLock initialization"""
        self.assertEqual(self.lock.username, self.username)
        self.assertIn(self.username, self.lock.lock_key)
        self.assertIn("vmg_v2:lock:token", self.lock.lock_key)
        self.assertEqual(self.lock.lock_timeout, 30000)  # 30s in ms
        self.assertEqual(self.lock.acquire_timeout, 5)
        self.assertIsNone(self.lock.owner_id)
    
    def test_acquire_immediate_success(self):
        """Test successful immediate lock acquisition"""
        # Mock Redis SET NX PX to return True (success)
        self.mock_redis.set.return_value = True
        self.mock_redis.incr.return_value = 1
        self.mock_redis.expire.return_value = True
        
        result = self.lock.acquire()
        
        self.assertTrue(result)
        self.assertIsNotNone(self.lock.owner_id)
        self.mock_redis.set.assert_called_once()
        # Verify SET NX PX parameters
        args, kwargs = self.mock_redis.set.call_args
        self.assertEqual(kwargs['nx'], True)
        self.assertEqual(kwargs['px'], 30000)
    
    def test_acquire_with_retry(self):
        """Test lock acquisition after retry"""
        # First call fails, second succeeds
        self.mock_redis.set.side_effect = [False, True]
        self.mock_redis.incr.return_value = 1
        self.mock_redis.expire.return_value = True
        
        with patch('time.sleep'):  # Mock sleep to speed up test
            result = self.lock.acquire()
        
        self.assertTrue(result)
        self.assertEqual(self.mock_redis.set.call_count, 2)
    
    def test_acquire_timeout(self):
        """Test lock acquisition timeout"""
        # Always return False (lock not acquired)
        self.mock_redis.set.return_value = False
        self.mock_redis.incr.return_value = 1
        self.mock_redis.expire.return_value = True
        
        # Set short timeout for test
        self.lock.acquire_timeout = 0.1
        
        result = self.lock.acquire()
        
        self.assertFalse(result)
        self.assertIsNotNone(self.lock.owner_id)  # owner_id is set even on failure
    
    def test_acquire_rate_limited(self):
        """Test rate limiting functionality"""
        # Mock rate limit exceeded
        self.mock_redis.incr.return_value = 100  # Exceeds MAX_ACQUIRE_ATTEMPTS_PER_MINUTE
        
        result = self.lock.acquire()
        
        self.assertFalse(result)
        self.mock_redis.incr.assert_called_once_with(self.lock.rate_limit_key)
    
    def test_release_success(self):
        """Test successful lock release"""
        # Setup lock as acquired
        self.lock.owner_id = "test-owner-id"
        self.mock_redis.eval.return_value = 1  # Lua script returns 1 (success)
        
        result = self.lock.release()
        
        self.assertTrue(result)
        self.assertIsNone(self.lock.owner_id)
        self.mock_redis.eval.assert_called_once()
    
    def test_release_not_owner(self):
        """Test release when not lock owner"""
        self.lock.owner_id = "test-owner-id"
        self.mock_redis.eval.return_value = 0  # Lua script returns 0 (not owner)
        
        result = self.lock.release()
        
        self.assertFalse(result)
        # owner_id should not be cleared if release failed
        self.assertIsNotNone(self.lock.owner_id)
    
    def test_release_without_owner_id(self):
        """Test release without owner_id"""
        self.lock.owner_id = None
        
        result = self.lock.release()
        
        self.assertFalse(result)
        self.mock_redis.eval.assert_not_called()
    
    def test_extend_lock_success(self):
        """Test successful lock extension"""
        self.lock.owner_id = "test-owner-id"
        self.mock_redis.eval.return_value = 1  # Success
        
        result = self.lock.extend_lock(5000)
        
        self.assertTrue(result)
        self.mock_redis.eval.assert_called_once()
    
    def test_extend_lock_not_owner(self):
        """Test lock extension when not owner"""
        self.lock.owner_id = "test-owner-id"
        self.mock_redis.eval.return_value = 0  # Not owner
        
        result = self.lock.extend_lock(5000)
        
        self.assertFalse(result)
    
    def test_is_locked(self):
        """Test lock status check"""
        self.mock_redis.exists.return_value = 1
        
        result = self.lock.is_locked()
        
        self.assertTrue(result)
        self.mock_redis.exists.assert_called_once_with(self.lock.lock_key)
    
    def test_get_lock_owner(self):
        """Test getting lock owner"""
        owner_id = "test-owner-123"
        self.mock_redis.get.return_value = owner_id.encode('utf-8')
        
        result = self.lock.get_lock_owner()
        
        self.assertEqual(result, owner_id)
        self.mock_redis.get.assert_called_once_with(self.lock.lock_key)
    
    def test_get_lock_ttl(self):
        """Test getting lock TTL"""
        ttl_ms = 15000
        self.mock_redis.pttl.return_value = ttl_ms
        
        result = self.lock.get_lock_ttl()
        
        self.assertEqual(result, ttl_ms)
        self.mock_redis.pttl.assert_called_once_with(self.lock.lock_key)
    
    def test_auto_extend_if_needed(self):
        """Test auto extension when TTL is low"""
        # Mock low TTL
        self.lock.owner_id = "test-owner-id"
        self.mock_redis.pttl.return_value = 3000  # 3 seconds left
        self.mock_redis.eval.return_value = 1  # Extend success
        
        result = self.lock.auto_extend_if_needed(min_ttl_ms=5000)
        
        self.assertTrue(result)
        self.mock_redis.eval.assert_called_once()  # extend_lock called
    
    def test_context_manager_success(self):
        """Test context manager successful flow"""
        self.mock_redis.set.return_value = True
        self.mock_redis.incr.return_value = 1
        self.mock_redis.expire.return_value = True
        self.mock_redis.eval.return_value = 1  # Release success
        
        with self.lock as lock_instance:
            self.assertIs(lock_instance, self.lock)
            self.assertIsNotNone(self.lock.owner_id)
        
        # After context, lock should be released
        self.assertIsNone(self.lock.owner_id)
    
    def test_context_manager_acquire_failure(self):
        """Test context manager when acquire fails"""
        self.mock_redis.set.return_value = False
        self.mock_redis.incr.return_value = 1
        self.mock_redis.expire.return_value = True
        self.lock.acquire_timeout = 0.1  # Short timeout
        
        with self.assertRaises(Exception) as cm:
            with self.lock:
                pass
        
        self.assertIn("Không thể chiếm lock", str(cm.exception))
    
    def test_redis_error_handling(self):
        """Test Redis error handling"""
        self.mock_redis.set.side_effect = redis.RedisError("Connection failed")
        self.mock_redis.incr.return_value = 1
        self.mock_redis.expire.return_value = True
        
        result = self.lock.acquire()
        
        self.assertFalse(result)


class TestLockMetrics(unittest.TestCase):
    def setUp(self):
        """Setup test fixtures"""
        self.metrics = LockMetrics()
        self.lock_key = "test:lock:key"
    
    def test_record_acquire_success(self):
        """Test recording successful acquire"""
        self.metrics.record_acquire_attempt(self.lock_key, True, 0.5)
        
        metrics = self.metrics.get_metrics(self.lock_key)
        self.assertEqual(metrics['acquire_count'], 1)
        self.assertEqual(metrics['acquire_success'], 1)
        self.assertEqual(metrics['total_acquire_time'], 0.5)
        self.assertEqual(metrics['max_acquire_time'], 0.5)
    
    def test_record_acquire_timeout(self):
        """Test recording acquire timeout"""
        self.metrics.record_acquire_attempt(self.lock_key, False, 5.0)
        
        metrics = self.metrics.get_metrics(self.lock_key)
        self.assertEqual(metrics['acquire_timeout'], 1)
        self.assertEqual(metrics['acquire_success'], 0)
    
    def test_record_acquire_error(self):
        """Test recording acquire error"""
        error = Exception("Redis error")
        self.metrics.record_acquire_attempt(self.lock_key, False, 1.0, error)
        
        metrics = self.metrics.get_metrics(self.lock_key)
        self.assertEqual(metrics['acquire_error'], 1)
        self.assertEqual(metrics['acquire_success'], 0)
    
    def test_thread_safety(self):
        """Test thread safety of metrics collection"""
        def record_metrics():
            for i in range(100):
                self.metrics.record_acquire_attempt(self.lock_key, True, 0.1)
        
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=record_metrics)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        metrics = self.metrics.get_metrics(self.lock_key)
        self.assertEqual(metrics['acquire_count'], 500)
        self.assertEqual(metrics['acquire_success'], 500)


if __name__ == '__main__':
    unittest.main()
