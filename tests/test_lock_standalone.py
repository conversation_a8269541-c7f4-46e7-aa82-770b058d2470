# coding: utf8
"""
Standalone tests cho VMGTokenLock và LockMetrics
Không phụ thuộc vào các module kh<PERSON>c của project
"""

import unittest
import time
import threading
import uuid
import random
from unittest.mock import Mock, patch, MagicMock
from collections import defaultdict
from threading import Lock as ThreadLock


class LockMetrics:
    """
    Thread-safe metrics collector cho distributed lock
    """
    def __init__(self):
        self._lock = ThreadLock()
        self._metrics = defaultdict(lambda: {
            'acquire_count': 0,
            'acquire_success': 0,
            'acquire_timeout': 0,
            'acquire_error': 0,
            'total_acquire_time': 0.0,
            'max_acquire_time': 0.0,
            'release_count': 0,
            'release_success': 0,
            'release_error': 0,
            'extend_count': 0,
            'extend_success': 0
        })
    
    def record_acquire_attempt(self, lock_key, success, duration, error=None):
        with self._lock:
            metrics = self._metrics[lock_key]
            metrics['acquire_count'] += 1
            metrics['total_acquire_time'] += duration
            metrics['max_acquire_time'] = max(metrics['max_acquire_time'], duration)
            
            if success:
                metrics['acquire_success'] += 1
            elif error:
                metrics['acquire_error'] += 1
            else:
                metrics['acquire_timeout'] += 1
    
    def record_release_attempt(self, lock_key, success):
        with self._lock:
            metrics = self._metrics[lock_key]
            metrics['release_count'] += 1
            if success:
                metrics['release_success'] += 1
            else:
                metrics['release_error'] += 1
    
    def record_extend_attempt(self, lock_key, success):
        with self._lock:
            metrics = self._metrics[lock_key]
            metrics['extend_count'] += 1
            if success:
                metrics['extend_success'] += 1
    
    def get_metrics(self, lock_key=None):
        with self._lock:
            if lock_key:
                return dict(self._metrics[lock_key])
            return {k: dict(v) for k, v in self._metrics.items()}
    
    def log_summary(self, lock_key):
        metrics = self.get_metrics(lock_key)
        if metrics['acquire_count'] > 0:
            avg_time = metrics['total_acquire_time'] / metrics['acquire_count']
            success_rate = metrics['acquire_success'] / metrics['acquire_count'] * 100
            
            print(f"Lock metrics for {lock_key}: "
                  f"attempts={metrics['acquire_count']}, "
                  f"success_rate={success_rate:.1f}%, "
                  f"avg_time={avg_time:.3f}s, "
                  f"max_time={metrics['max_acquire_time']:.3f}s")


# Global metrics instance
lock_metrics = LockMetrics()


class VMGTokenLock:
    """
    Redis distributed-lock cho VMG v2 token với cải thiện performance và reliability
    """
    
    # Rate limiting constants
    MAX_ACQUIRE_ATTEMPTS_PER_MINUTE = 60
    RATE_LIMIT_WINDOW = 60  # seconds
    
    def __init__(self, redis_client, username=None, lock_timeout=30, acquire_timeout=5):
        self.redis = redis_client
        self.username = username or "default"
        # Secure lock key với namespace và hash
        username_hash = hash(self.username) % 10000  # Simple hash for uniqueness
        self.lock_key = f"vmg_v2:lock:token:{username_hash}:{self.username}"
        self.rate_limit_key = f"vmg_v2:rate_limit:lock:{username_hash}:{self.username}"
        self.lock_timeout = lock_timeout * 1000  # Convert to milliseconds
        self.acquire_timeout = acquire_timeout
        self.owner_id = None
        
        # Lua script để release an toàn
        self.release_script = """
        if redis.call('GET', KEYS[1]) == ARGV[1] then
            return redis.call('DEL', KEYS[1])
        else
            return 0
        end
        """
        
        # Lua script để extend lock
        self.extend_script = """
        if redis.call('GET', KEYS[1]) == ARGV[1] then
            return redis.call('PEXPIRE', KEYS[1], ARGV[2])
        else
            return 0
        end
        """
    
    def _check_rate_limit(self):
        """
        Check rate limiting cho acquire attempts
        Returns: True nếu trong limit, False nếu vượt quá
        """
        try:
            current_count = self.redis.incr(self.rate_limit_key)
            if current_count == 1:
                # Set TTL cho key mới
                self.redis.expire(self.rate_limit_key, self.RATE_LIMIT_WINDOW)
            
            if current_count > self.MAX_ACQUIRE_ATTEMPTS_PER_MINUTE:
                print(f"Rate limit exceeded for {self.username}: {current_count} attempts")
                return False
            return True
        except Exception as e:
            print(f"Error checking rate limit: {e}")
            # Cho phép tiếp tục nếu Redis error
            return True
    
    def acquire(self):
        """
        Chiếm lock với exponential backoff, rate limiting và improved error handling
        Returns: True nếu thành công, False nếu timeout hoặc rate limited
        """
        # Check rate limiting trước
        if not self._check_rate_limit():
            return False
            
        self.owner_id = str(uuid.uuid4())
        start_time = time.time()
        attempt = 0
        success = False
        error = None
        
        try:
            # Thử ngay lập tức
            if self.redis.set(self.lock_key, self.owner_id, nx=True, px=self.lock_timeout):
                success = True
                print(f"Lock acquired immediately: {self.lock_key}")
                return True
            
            # Poll với exponential backoff
            while (time.time() - start_time) < self.acquire_timeout:
                # Exponential backoff với jitter
                sleep_time = min(0.1 * (2 ** attempt), 1.0)
                jitter = random.uniform(0, 0.1)
                time.sleep(sleep_time + jitter)
                
                if self.redis.set(self.lock_key, self.owner_id, nx=True, px=self.lock_timeout):
                    success = True
                    acquire_time = time.time() - start_time
                    print(f"Lock acquired after {acquire_time:.3f}s, attempts: {attempt + 1}")
                    return True
                
                attempt += 1
            
            # Timeout
            print(f"Lock acquire timeout after {self.acquire_timeout}s, attempts: {attempt + 1}")
            return False
            
        except Exception as e:
            error = e
            print(f"Error during lock acquire: {e}")
            return False
        finally:
            # Record metrics
            duration = time.time() - start_time
            lock_metrics.record_acquire_attempt(self.lock_key, success, duration, error)
    
    def release(self):
        """
        Release lock an toàn (chỉ owner mới xóa được)
        """
        if not self.owner_id:
            print("Attempting to release lock without owner_id")
            lock_metrics.record_release_attempt(self.lock_key, False)
            return False
        
        success = False
        try:
            result = self.redis.eval(
                self.release_script, 
                1, 
                self.lock_key, 
                self.owner_id
            )
            
            if result == 1:
                success = True
                print(f"Lock released successfully: {self.lock_key}")
                self.owner_id = None
                return True
            else:
                print(f"Failed to release lock (not owner): {self.lock_key}")
                return False
                
        except Exception as e:
            print(f"Error during lock release: {e}")
            return False
        finally:
            lock_metrics.record_release_attempt(self.lock_key, success)
    
    def extend_lock(self, additional_time_ms=5000):
        """
        Extend lock nếu vẫn được sở hữu bởi instance này
        Returns: True nếu thành công, False nếu không sở hữu lock
        """
        if not self.owner_id:
            lock_metrics.record_extend_attempt(self.lock_key, False)
            return False
            
        success = False
        try:
            result = self.redis.eval(
                self.extend_script, 
                1, 
                self.lock_key, 
                self.owner_id, 
                additional_time_ms
            )
            
            if result == 1:
                success = True
                print(f"Lock extended by {additional_time_ms}ms: {self.lock_key}")
                return True
            else:
                print(f"Failed to extend lock (not owner): {self.lock_key}")
                return False
                
        except Exception as e:
            print(f"Error during lock extend: {e}")
            return False
        finally:
            lock_metrics.record_extend_attempt(self.lock_key, success)
    
    def is_locked(self):
        """
        Check nếu lock hiện tại đang được giữ
        """
        try:
            return self.redis.exists(self.lock_key) == 1
        except Exception as e:
            print(f"Error checking lock status: {e}")
            return False
    
    def get_lock_owner(self):
        """
        Lấy owner hiện tại của lock
        """
        try:
            owner = self.redis.get(self.lock_key)
            return owner.decode('utf-8') if owner else None
        except Exception as e:
            print(f"Error getting lock owner: {e}")
            return None
    
    def get_lock_ttl(self):
        """
        Lấy TTL còn lại của lock (milliseconds)
        """
        try:
            ttl = self.redis.pttl(self.lock_key)
            return ttl if ttl > 0 else 0
        except Exception as e:
            print(f"Error getting lock TTL: {e}")
            return 0
    
    def auto_extend_if_needed(self, min_ttl_ms=5000, extend_by_ms=10000):
        """
        Tự động extend lock nếu TTL còn lại ít hơn min_ttl_ms
        Returns: True nếu extended hoặc không cần extend, False nếu failed
        """
        try:
            current_ttl = self.get_lock_ttl()
            if 0 < current_ttl < min_ttl_ms:
                print(f"Auto-extending lock {self.lock_key}, TTL: {current_ttl}ms")
                return self.extend_lock(extend_by_ms)
            return True  # Không cần extend hoặc lock đã hết hạn
        except Exception as e:
            print(f"Error in auto_extend_if_needed: {e}")
            return False
    
    def __enter__(self):
        if not self.acquire():
            raise Exception(f"Không thể chiếm lock sau {self.acquire_timeout}s")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()
        # Log metrics summary
        lock_metrics.log_summary(self.lock_key)
        # Log nếu có exception trong context
        if exc_type:
            print(f"Exception in lock context: {exc_type.__name__}: {exc_val}")


class TestVMGTokenLock(unittest.TestCase):
    def setUp(self):
        """Setup test fixtures"""
        self.mock_redis = Mock()
        self.username = "test_user"
        self.lock = VMGTokenLock(self.mock_redis, self.username)
    
    def test_init(self):
        """Test VMGTokenLock initialization"""
        self.assertEqual(self.lock.username, self.username)
        self.assertIn(self.username, self.lock.lock_key)
        self.assertIn("vmg_v2:lock:token", self.lock.lock_key)
        self.assertEqual(self.lock.lock_timeout, 30000)  # 30s in ms
        self.assertEqual(self.lock.acquire_timeout, 5)
        self.assertIsNone(self.lock.owner_id)
    
    def test_acquire_immediate_success(self):
        """Test successful immediate lock acquisition"""
        # Mock Redis SET NX PX to return True (success)
        self.mock_redis.set.return_value = True
        self.mock_redis.incr.return_value = 1
        self.mock_redis.expire.return_value = True
        
        result = self.lock.acquire()
        
        self.assertTrue(result)
        self.assertIsNotNone(self.lock.owner_id)
        self.mock_redis.set.assert_called_once()
    
    def test_acquire_rate_limited(self):
        """Test rate limiting functionality"""
        # Mock rate limit exceeded
        self.mock_redis.incr.return_value = 100  # Exceeds MAX_ACQUIRE_ATTEMPTS_PER_MINUTE
        
        result = self.lock.acquire()
        
        self.assertFalse(result)
        self.mock_redis.incr.assert_called_once_with(self.lock.rate_limit_key)
    
    def test_release_success(self):
        """Test successful lock release"""
        # Setup lock as acquired
        self.lock.owner_id = "test-owner-id"
        self.mock_redis.eval.return_value = 1  # Lua script returns 1 (success)
        
        result = self.lock.release()
        
        self.assertTrue(result)
        self.assertIsNone(self.lock.owner_id)
        self.mock_redis.eval.assert_called_once()


class TestLockMetrics(unittest.TestCase):
    def setUp(self):
        """Setup test fixtures"""
        self.metrics = LockMetrics()
        self.lock_key = "test:lock:key"
    
    def test_record_acquire_success(self):
        """Test recording successful acquire"""
        self.metrics.record_acquire_attempt(self.lock_key, True, 0.5)
        
        metrics = self.metrics.get_metrics(self.lock_key)
        self.assertEqual(metrics['acquire_count'], 1)
        self.assertEqual(metrics['acquire_success'], 1)
        self.assertEqual(metrics['total_acquire_time'], 0.5)
        self.assertEqual(metrics['max_acquire_time'], 0.5)


if __name__ == '__main__':
    unittest.main()
