#!/usr/bin/env python
# -*- coding: utf-8 -*-

from app.libs.apis.third_party_api.behavior_appota_api import Behavior<PERSON>ppota<PERSON>pi
from flask_restful import Resource
from flask_restful.reqparse import Argument
from loguru import logger
from app.const import DEFINE_SUPPLIER_VMG_V2
from app.utils import remove_none_in_dict
from app.api import CodexService
from app.decorators import sqlalchemy_session, parse_params
from app.extensions import db
from app.libs.apis.third_party_api.behavior_grab_api import BehaviorGrabApi
from app.libs.apis.third_party_api.behavior_vmg_v2_api import BehaviorVmgApiV2



class FlaskGetCode(Resource):
    @parse_params(
        Argument(
            "products",
            location=["values", "json"],
            required=True,
            help="products",
            type=dict,
            default=None,
            action="append"
        )
    )
    @sqlalchemy_session(db)
    def post(self, **kwargs):
        try:
            logger.info("Request")
            logger.info(kwargs)
            codes = CodexService.get(**remove_none_in_dict(kwargs))
            if codes is not False:
                return {"success": True, "code": 200, "data": codes}, 200
            else:
                return {
                    "success": False,
                    "code": 400,
                    "message": "Input error",
                    "error": {"errors": "Dữ liệu truyền lên không đúng định dạng"},
                }, 400
        except Exception as e:
            logger.exception(e)


class FlaskGetCodeByOrder(Resource):
    @parse_params(
        Argument(
            "quantity",
            location=["values", "json"],
            required=True,
            help="quantity",
            type=int,
            default=None,
        ),
        Argument(
            "product_id",
            location=["values", "json"],
            required=True,
            help="product_id",
            type=int,
            default=None,
        ),
        Argument(
            "product_supplier_id",
            location=["values", "json"],
            required=True,
            help="product_supplier_id",
            type=int,
            default=None,
        ),
        Argument(
            "start_date",
            location=["values", "json"],
            required=False,
            help="start_date",
            type=int,
            default=None,
        ),
        Argument(
            "end_date",
            location=["values", "json"],
            required=False,
            help="end_date",
            type=int,
            default=None,
        ),
    )
    @sqlalchemy_session(db)
    def post(self, **kwargs):
        logger.info('start get_code_by_order')
        res = CodexService.get_code_by_order(**remove_none_in_dict(kwargs))
        if res is not None:
            return {"success": True, "code": 200, "data": res}, 200
        else:
            return {
                "success": False,
                "code": 400,
                "message": "Input error",
                "error": {"errors": "Dữ liệu truyền lên không đúng định dạng"},
            }, 400


class FlaskGetCodeWebhook(Resource):
    @parse_params(
        Argument(
            "request_id",
            location=["values", "json"],
            required=True,
            help="request_id",
            type=str,
            default=None,
        ),
        Argument(
            "status",
            location=["values", "json"],
            required=True,
            help="status",
            type=str,
            default=None,
        ),
        Argument(
            "detail",
            location=["values", "json"],
            required=False,
            help="data response",
            type=dict,
            default=None,
        ),
    )
    @sqlalchemy_session(db)
    def post(self, **kwargs):
        logger.info(f"webhook request: {kwargs}")
        res = CodexService.webhook(**remove_none_in_dict(kwargs))
        if res is True:
            return True, 202
        else:
            return {
                "success": False,
                "code": 400,
                "message": "Input error",
                "error": {"errors": "Dữ liệu truyền lên không đúng định dạng"},
            }, 400


class FlaskVietguysWebhook(Resource):
    @parse_params(
        Argument(
            "error",
            location=["values", "json"],
            required=True,
            help="error code",
            type=int,
            default=None,
        ),
        Argument(
            "message",
            location=["values", "json"],
            required=True,
            help="message help",
            type=str,
            default=None,
        ),
        Argument(
            "data",
            location=["values", "json"],
            required=False,
            help="data response",
            type=dict,
            default=None,
        ),
    )
    @sqlalchemy_session(db)
    def post(self, **kwargs):
        logger.info(f"VGS request: {kwargs}")
        res = CodexService.vietguys_webhook(**remove_none_in_dict(kwargs))
        if res is True:
            return True, 202
        else:
            return {
                "success": False,
                "code": 400,
                "message": "Input error",
                "error": {"errors": "Dữ liệu truyền lên không đúng định dạng"},
            }, 400


class FlaskGrabWebhook(Resource):
    @parse_params(
        Argument(
            "txType",
            location=["values", "json"],
            required=True,
            help="txType",
            type=str,
            default=None,
        ),
        Argument(
            "partnerReference",
            location=["values", "json"],
            required=True,
            help="partnerReference",
            type=str,
            default=None,
        ),
        Argument(
            "txID",
            location=["values", "json"],
            required=False,
            help="grab's txID",
            type=str,
            default=None,
        ),
        Argument(
            "txStatus",
            location=["values", "json"],
            required=False,
            help="grab's txStatus",
            type=str,
            default=None,
        ),
        Argument(
            "meta",
            location=["values", "json"],
            required=False,
            help="grab's txStatus",
            type=dict,
            default=None,
        ),
        Argument(
            "createdAt",
            location=["values", "json"],
            required=False,
            help="grab's createdAt",
            type=int,
            default=None,
        ),
        Argument(
            "completedAt",
            location=["values", "json"],
            required=False,
            help="grab's completedAt",
            type=int,
            default=None,
        ),
    )
    @sqlalchemy_session(db)
    def post(self, **kwargs):
        logger.info(f"Grab request: {kwargs}")
        res = CodexService.grab_webhook(**remove_none_in_dict(kwargs))
        if res is True:
            return True, 200
        else:
            return {
                "success": False,
                "code": 400,
                "message": "Input error",
                "error": {"errors": "Dữ liệu truyền lên không đúng định dạng"},
            }, 400

class FlaskPOOrderCode(Resource):
    @parse_params(
        Argument(
            "products",
            location=["values", "json"],
            required=True,
            help="products",
            type=dict,
            default=None,
            action="append"
        ),
        Argument(
            "request_id",
            location=["values", "json"],
            required=True,
            help="request_id",
            type=str,
            default=None,
        ),
        Argument(
            "po",
            location=["values", "json"],
            required=False,
            help="po",
            type=str,
            default=None,
        ),
        Argument(
            "supplier_id",
            location=["values", "json"],
            required=False,
            help="supplier_id",
            type=int,
            default=None,
        ),
        Argument(
            "contract_code",
            location=["values", "json"],
            required=False,
            help="contract_code",
            type=str,
            default=None,
        ),
        Argument(
            "extra_info",
            location=["values", "json"],
            required=False,
            help="extra_info",
            type=dict,
            default=None,
        ),
    )
    @sqlalchemy_session(db)
    def post(self, **kwargs):
        res = CodexService.urcard_order(**remove_none_in_dict(kwargs))
        if not res:
            return {
                "success": False,
                "code": 400,
                "error": {"errors": "Đã có lỗi xảy ra"},
            }, 400
        return {
            "success": True,
            "code": 200,
            "data": res
        }, 200

class FlaskTriggerRetryGetCode(Resource):
    @parse_params(
        Argument(
            name="transaction_id",
            location=["values", "json"],
            required=True,
            help="transaction_id",
            type=str,
            default=None,
        )
    )
    @sqlalchemy_session(db)
    def post(self, **kwargs):
        try:
            CodexService.retry_get_code(**kwargs)
            return {"success": True, "code": 200}, 200
        except Exception as e:
            logger.exception(e)
            return {"success": False, "code": 400, "error": str(e)}, 400


class FlaskRetryPOOrderCode(Resource):
    @parse_params(
        Argument(
            "products",
            location=["values", "json"],
            required=True,
            help="products",
            type=dict,
            default=None,
            action="append"
        ),
        Argument(
            "request_id",
            location=["values", "json"],
            required=True,
            help="request_id",
            type=str,
            default=None,
        )
    )
    @sqlalchemy_session(db)
    def post(self, **kwargs):
        res = CodexService.urcard_retry_order(**remove_none_in_dict(kwargs))
        if not res:
            return {
                "success": False,
                "code": 400,
                "error": {"errors": "Đã có lỗi xảy ra"},
            }, 400
        return {
            "success": True,
            "code": 200,
            "data": res
        }, 200
class FlaskCheckBalance(Resource):
    @staticmethod
    def get(vendor_name):
        if vendor_name == DEFINE_SUPPLIER_VMG_V2:
            vendor = BehaviorVmgApiV2()
        elif vendor_name == 'grab':
            vendor = BehaviorGrabApi()
        elif vendor_name == "appota":
            vendor = BehaviorAppotaApi()
        else:
            return {"success": False, "code": 400, "message": "Vendor name is not valid", "status": 2}, 400
        balance_info = vendor.get_balance()
        return {"success": True, "code": 200, "message": "pong", "data": balance_info, "status": 2}, 200
