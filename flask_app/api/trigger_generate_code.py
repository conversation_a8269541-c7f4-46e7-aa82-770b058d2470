#!/usr/bin/env python
# -*- coding: utf-8 -*-
from flask_restful import Resource
from flask_restful.reqparse import Argument
from loguru import logger

from app.config import config_app
from app.extensions import kafka_producer
from app.decorators import sqlalchemy_session, parse_params
from app.extensions import db
from app.security import VmgTripleDes
# from kafka_app.consumer_worker.consumer_generate_code import ConsumerGenerateCode

class FlaskTriggerGenerateCode(Resource):
    @parse_params(
        Argument(
            name="product_ids",
            location=["values", "json"],
            required=True,
            help="product_ids",
            type=str,
            default=None,
            action="append"
        )
    )
    @sqlalchemy_session(db)
    def post(self, **kwargs):

        try:
            payload = []
            for product_id in kwargs.get("product_ids"):
                payload.append({
                    "product_id": product_id,
                })
            #ConsumerGenerateCode.running([{'product_id': 10179, 'quantity': 1}])
            kafka_producer.push(
                config_app.TOPIC_GENERATE_CODE,
                {
                    "type": "GENERATE_CODE",
                    "payload": payload,
                },
            )
            return {"success": True, "code": 200}, 200
        except Exception as e:
            logger.exception(e)
