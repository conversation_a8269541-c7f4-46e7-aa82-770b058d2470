#!/usr/bin/env python
# -*- coding: utf-8 -*-
from app import models as m
from app.const import STATUS_ON, PROCESS_SENT
from app.extensions import db
from app.helper import Helper


class OrderDetailRepositories(object):
    def __init__(self):
        self.model = m.OrderDetail

    # TODO: refactor all code
    def create(self, *args, **kwargs):
        creator = self.model(
            order_id=kwargs.get("order_id") or 0,
            supplier_order_id=kwargs.get("supplier_order_id") or 0,
            order_code=kwargs.get("order_code") or '',
            request=kwargs.get("request") or '',
            response=kwargs.get("response") or '',
            response_po=kwargs.get("response_po") or '',
            transaction_id=kwargs.get("transaction_id") or '',
            action=kwargs.get("action") or PROCESS_SENT,
            status=kwargs.get("status") or STATUS_ON,
            callback_body=kwargs.get("callback_body") or '',
            request_url=kwargs.get("request_url") or '',
            header=kwargs.get("header") or '',
            http_code=kwargs.get("http_code") or '',
            quantity=kwargs.get("quantity") or '',
            created=kwargs.get("created") or Helper.get_now_unix_timestamp(),
            updated=kwargs.get("updated") or Helper.get_now_unix_timestamp(),
        )
        creator.save()
        return creator

    def get_by_tid(self, tid):
        return self.model.query().filter(self.model.transaction_id == tid).first()

    def get_by_order_code(self, order_code):
        return self.model.query().filter(self.model.order_code == order_code).first()

    def __filter_query(self, **kwargs):
        query = self.model.query()
        for attr, value in kwargs.items():
            if hasattr(self.model, attr):
                column = getattr(self.model, attr)
                query = query.filter(column == value)
        return query

    def get_with_condition(self, **kwargs):
        return self.__filter_query(**kwargs).order_by(self.model.id.desc()).first()

    def get_list_with_condition(self, **kwargs):
        return self.__filter_query(**kwargs).order_by(self.model.id.desc()).all()

    def list_by_order_id(self, order_id, action):
        return self.model.query().populate_existing().filter(self.model.order_id == order_id, self.model.action == action).all()


order_detail_repo = OrderDetailRepositories()
