#!/usr/bin/env python
# -*- coding: utf-8 -*-

# Services
import os
APP_CONFIG = os.environ.get("APP_CONFIG") or "DEVELOP"

SERVICE_DOMAIN_A6_PRODUCT = (
        os.environ.get("SERVICE_DOMAIN_A6_PRODUCT") or "something-domain-a6"
)
SERVICE_DOMAIN_A8_DOCUMENT = (
        os.environ.get("SERVICE_DOMAIN_A8_DOCUMENT") or "something-domain-a8"
)
SERVICE_DOMAIN_A16_ROUTE = (
        os.environ.get("SERVICE_DOMAIN_A16_ROUTE") or "something-domain-a16"
)
SERVICE_DOMAIN_A18_URBOX = (
        os.environ.get("SERVICE_DOMAIN_A18_URBOX") or "something-domain-a18"
)
ROOT_URL_A21 = os.environ.get("ROOT_URL_A21") or "something-domain-a21"

SERVICE_DOMAIN_SYSLOG_URBOX = (
        os.environ.get("SYSLOG_HOST") or "smt-domain-syslog"
)

SERVICE_DOMAIN_SYSLOG_URBOX_ACTION = (
        os.environ.get("PATH_COMMON_LOG") or "path-common-log"
)

SERVICE_DOMAIN_URCARD = (
        os.environ.get("URCARD_HOST") or "smt-domain-syslog"
)

SERVICE_DOMAIN_URCARD_ACTION_ORDER = (
        os.environ.get("PATH_URCARD_ORDER_CODE") or "path-common-log"
)

SERVICE_DOMAIN_URCARD_PROFILE_ID = (
        os.environ.get("URCARD_PROFILE_ID") or "urcard-profile-id"
)

SERVICE_DOMAIN_URCARD_ACCESS_TOKEN = (
        os.environ.get("URCARD_ACCESS_TOKEN") or "urcard-access-token"
)

SERVICE_SYSTEM_LOG_SERVICE_SECRET_KEY = (
        os.environ.get("SYSTEM_LOG_SERVICE_SECRET_KEY") or "syslog-secret-key"
)

SERVICE_VERSION_A6_PRODUCT = "v1"
SERVICE_VERSION_A8_DOCUMENT = "v1"
SERVICE_VERSION_A16_ROUTE = "v1"
SERVICE_VERSION_A18_URBOX = "v1"
SERVICE_VERSION_A21 = "v1"

SERVICE_ACTION_A6_PRODUCT_NEED_GET_CODE = "product-need-get-code"
SERVICE_ACTION_A6_PRODUCT_UPDATE = "product"
SERVICE_ACTION_A6_PRODUCT_GET = "product"
SERVICE_ACTION_A6_PRODUCT_SUPPLIER = "product-supplier"

SERVICE_ACTION_A8_READ_EXCEL = "read-excel"
SERVICE_ACTION_A8_DOWNLOAD = "download"
SERVICE_ACTION_A8_UPLOAD = "upload"

SERVICE_ACTION_A18_URBOX_USED_CODE = "used-code"
SERVICE_ACTION_A18_URBOX_STATUS_CODE = "status-code"
SERVICE_ACTION_A18_URBOX_GET_DISCOUNT_TYPE = "get-discount-type"
SERVICE_ACTION_A18_URBOX_GET_DISCOUNT_INFO = "get-discount-info"
SERVICE_ACTION_A18_URBOX_UPDATE_CODE = "update-code"
SERVICE_ACTION_A18_URBOX_GET_PRODUCT_BY_CODE = "get-product-by-code"
SERVICE_ACTION_A18_URBOX_GET_EMAIL_BY_JOB_AND_SUPPLIER = "get-email-by-job-and-supplier"
SERVICE_ACTION_A18_URBOX_SUPPLIER_INFO = "supplier"
SERVICE_ACTION_A18_CREATE_SUPPLIER_ORDER = "create-supplier-order"
SERVICE_ACTION_A18_UPDATE_SUPPLIER_ORDER = "update-supplier-order"
SERVICE_ACTION_A18_CREATE_MULTI_ORDER_DETAIL = "create-multi-order-detail"

A21_CREATE_SUPPLIER_ORDER = "create_supplier_order"
A21_UPDATE_SUPPLIER_ORDER = "update_supplier_order"
A21_CREATE_MULTI_ORDER_DETAIL = "create_multi_supplier_order_detail"
A21_UPDATE_PROCESS_SUPPLIER_ORDER = "update_process_supplier_order"
A21_CREATE_SUPPLIER_ORDER_DETAIL = "create_supplier_order_detail"
A21_GET_SUPPLIER_ORDER = "get_supplier_order"

SERVICE_CONSTANT_A18_UPDATE_CODE_TYPE_CROSS_CHECK = 1
SERVICE_CONSTANT_A18_UPDATE_CODE_TYPE_PAYED = 2

SERVICE_ACTION_POST_SUPPLIER_ORDER_CREATE = "create-order-detail"
SERVICE_ACTION_GET_SUPPLIER_ORDER_PROCESS_PENDING = "get-supplier-order"
SERVICE_ACTION_POST_SUPPLIER_ORDER_PROCESS_UPDATE = "update-process"

SERVICE_ID = 12
SERVICE_NAME_ROUTER_PRODUCT = "storage"

# Flask celery task job
REDIS_KEY_FLASK_CELERY_TASK = "A12:FLASK_CELERY_TASK:FLAG"
REDIS_KEY_CACHE_CODES = "A12:CACHES:CODES_AUTO_GENERATE"
REDIS_KEY_STORE_CODE = "A12:STORES:CODE:"
REDIS_KEY_CACHE_CODES_EXPIRED = 300
FLASK_CELERY_TASK_SLEEP = 0.01
FLASK_CELERY_TASK_LIMIT = 3
FLASK_CELERY_PAGE_LIMIT = 20
PER_PAGE_LIMIT = 100
LIMIT_NUMBER_OF_CODEX = 100
LIMIT_NUMBER_OF_CODEX_ORDER = 2000
REDIS_KEY_CONFIG_PREFIX = "A12:CONFIG:"
REDIS_KEY_CONFIG_EXPIRED = 86400
QUANTITY_CONFIG_KEY = "quantity"
CODE_AVAILABLE_DELTA_DAYS = 7

DEFINE_SUPPLIER_NHANVAN = "nhanvan_v1"
DEFINE_SUPPLIER_7ELEVEN = "7eleven_v1"
DEFINE_SUPPLIER_HOAYEUTHUONG = "hoayeuthuong_v1"
DEFINE_SUPPLIER_IOMEDIA = "iomedia_v1"
DEFINE_SUPPLIER_JOLLIBEE = "jollibee_v1"
DEFINE_SUPPLIER_THECOFFEEHOUSE = "thecoffeehouse_v1"
DEFINE_SUPPLIER_GRAB = "grab_v1"
DEFINE_SUPPLIER_VINID = "vinid_v1"
DEFINE_SUPPLIER_VMG = "vmg_v1"
DEFINE_SUPPLIER_VTC = "vtc_v1"
DEFINE_SUPPLIER_GIFT_POP = "gift_pop_v1"
DEFINE_SUPPLIER_EZIN = "ezin_v1"
DEFINE_SUPPLIER_SHOPEE = "shopee_v1"
DEFINE_SUPPLIER_VIETGUYS = "vietguys_v1"
DEFINE_SUPPLIER_URBOX = "urbox_v1"
DEFINE_SUPPLIER_SHOPEE_FOOD = "shopee_food_v1"
DEFINE_SUPPLIER_VIETGUYS_V4 = "vietguys_v4"
# COMMON
PER_PAGE = 20  # Pagination
GENERATE_CODE_QUANTITY_LIMIT = 1000  # Codes
GENERATE_CODE_LENGTH_LIMIT = 9
LIMIT_GET_CODE_VGS = 100
LIMIT_GET_CODE_GRAB = 1000
# GENERATE_CODE_ALLOWED_CHARS = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
GENERATE_CODE_ALLOWED_CHARS = "0123456789"
DAY = 24 * 60 * 60
LOCAL_TIME_ZONE = "Asia/Bangkok"
LIMIT_TIME_RUNNING_TASK = 3 * 60
LIMIT_CODES = 1000

# database
STATUS_ON = 2
STATUS_OFF = 1
PROCESS_NEW = 111
PROCESS_HOLD = 112
PROCESS_SOLD = 210
PROCESS_USED = 220
SQL_FORMAT_DATETIME = "%Y-%m-%d %H:%M:%S"
SAVE_CODE_AS_HELD = 1
SAVE_CODE_AS_SOLD = 2
PROCESS_SENT = 1
PROCESS_SUCCESS = 2
PROCESS_FAIL = 3
VGS_GET_CODE_WEBHOOK_SUCCESS = 0
SUPPLIER_ORDER_PROCESS_SUCCESS=3
SUPPLIER_ORDER_PROCESS_FAIL=4

GET_CODE_WEBHOOK_SUCCESS = 1
GET_CODE_WEBHOOK_FAILED = 2
# Quang Anh bảo đặt tên như thế nayf
LIMIT_EXPIRED_DATE = os.environ.get('LIMIT_EXPIRED_DATE', 7)

# Jollibee
JOLLIBEE_EXPIRED_DAYS = 130

# Excel format
EXCEL_FORMAT_IMPORT_CODE_URBOX = ["Mã quà tặng", "Hạn sử dụng", "Serial", "Pin"]

# Excel Merchant Config
EXCEL_IMPORT_CODE_URBOX_CODEX = 0
EXCEL_IMPORT_CODE_URBOX_EXPIRED = 1
EXCEL_IMPORT_CODE_URBOX_SERIAL = 2
EXCEL_IMPORT_CODE_URBOX_PIN = 3

# API Get Code For Sell
MAXIMUM_PRODUCT_REQUEST = 5
MAXIMUM_PRODUCT_QUANTITY = 100
MINIMUM_PRODUCT_QUANTITY = 20
REDIS_CODEX_KEY = "A12:STORES:CODE"
REDIS_SUPPLIER_KEY = "A12:PRODUCT_INFO"
REDIS_FLAG_GET_CODE_ORDER_KEY = "A12:FLAG_GET_CODE_ORDER_KEY"
REDIS_SUPPLIER_EXPIRED = 10800
# Message config
MSG_SUCCESS = "Thành công (Successful)."
MSG_REQUIRED = "Bắt buộc (is required)."
MSG_NOT_FOUND_DEFAULT = "Không tìm thấy (Not found)."
# Flag
REDIS_FLAG_KAFKA = "A12:KAFKA_FLAG:GENERATE_CODE"
REDIS_FLAG_PRODUCT = "A12:PRODUCT:FLAG"
# Codex to Redis first time
REDIS_PRODUCT_ID_LIST = "A12:PRODUCT:LIST"

##
# Third party apis
##
# The coffee house
URBOX_AUTHORIZATION = os.environ.get("URBOX_AUTHORIZATION") or "something-authorization"
THIRD_PARTY_THE_COFFEE_HOUSE_HOST = (
        os.environ.get("THE_COFFEE_HOUSE_HOST") or "something-the-coffeehouse-host"
)
THIRD_PARTY_THE_COFFEE_HOUSE_PATH_GET_CODE = (
        os.environ.get("THE_COFFEE_HOUSE_PATH_GET_CODE")
        or "something-the-coffeehouse-path-get-code"
)
THIRD_PARTY_THE_COFFEE_HOUSE_SECRET_CODE = (
        os.environ.get("THE_COFFEE_HOUSE_SECRET_CODE")
        or "something-the-coffeehouse-secret-code"
)
THIRD_PARTY_THE_COFFEE_HOUSE_AUTHOR_CODE = (
        os.environ.get("THE_COFFEE_HOUSE_AUTHOR_CODE")
        or "something-the-coffeehouse-author-code"
)

# Jollibee
THIRD_PARTY_JOLLIBEE_SECRET_CODE = (
        os.environ.get("JOLLIBEE_SECRET_CODE") or "something-jollibee-secret-code"
)
THIRD_PARTY_JOLLIBEE_AUTHOR_CODE = (
        os.environ.get("JOLLIBEE_AUTHOR_CODE") or "something-jollibee-author-code"
)
THIRD_PARTY_JOLLIBEE_HOST = os.environ.get("JOLLIBEE_HOST") or "something-jollibee-host"
THIRD_PARTY_JOLLIBEE_PATH_GET_CODE = (
        os.environ.get("JOLLIBEE_PATH_GET_CODE") or "something-jollibee-path-get-code"
)
THIRD_PARTY_JOLLIBEE_PATH_DELETE = (
        os.environ.get("JOLLIBEE_PATH_DELETE") or "something-jollibee-path-delete"
)
THIRD_PARTY_JOLLIBEE_API_KEY = (
        os.environ.get("JOLLIBEE_API_KEY") or "something-jollibee-path-api-key"
)

# IO Media
THIRD_PARTY_IO_MEDIA_HOST = os.environ.get("IO_MEDIA_HOST") or "something-io-media-host"
THIRD_PARTY_IO_MEDIA_PATH_GET_CODE = (
        os.environ.get("IO_MEDIA_PATH_GET_CODE") or "something-io-media-path-get-code"
)
THIRD_PARTY_IO_MEDIA_USERNAME = (
        os.environ.get("IO_MEDIA_AUTHOR_CODE") or "something-io-media-username"
)
THIRD_PARTY_IO_MEDIA_EMAIL_RECEIVE = (
        os.environ.get("IO_MEDIA_EMAIL_RECEIVE") or "something-io-media-email-receive"
)
THIRD_PARTY_IO_MEDIA_PATH_CHECK_BALANCE = (
        os.environ.get("IO_MEDIA_PATH_CHECK_BALANCE")
        or "something-io-media-path-check-balance"
)

# Hoa Yêu Thương
THIRD_PARTY_HOA_YEU_THUONG_HOST = (
        os.environ.get("HOA_YEU_THUONG_HOST") or "something-hoayeuthuong-host"
)
THIRD_PARTY_HOA_YEU_THUONG_PASSWORD = (
        os.environ.get("HOA_YEU_THUONG_PASSWORD") or "something-hoayeuthuong-password"
)
THIRD_PARTY_HOA_YEU_THUONG_USERNAME = (
        os.environ.get("HOA_YEU_THUONG_USERNAME") or "something-hoayeuthuong-username"
)
THIRD_PARTY_HOA_YEU_THUONG_PATH_GET_CODE = (
        os.environ.get("HOA_YEU_THUONG_PATH_GET_CODE")
        or "something-hoayeuthuong-path-get-code"
)
THIRD_PARTY_HOA_YEU_THUONG_PATH_GET_ONE_CODE = (
        os.environ.get("HOA_YEU_THUONG_PATH_GET_ONE_CODE")
        or "something-hoayeuthuong-path-get-one-code"
)

# Seven Eleven
THIRD_PARTY_7ELEVEN_HOST = os.environ.get("ELEVEN_HOST") or "something-7eleven-host"
THIRD_PARTY_7ELEVEN_TOKEN = os.environ.get("ELEVEN_TOKEN") or "something-7eleven-token"
THIRD_PARTY_7ELEVEN_PATH_GET_CODE = (
        os.environ.get("ELEVEN_PATH_GET_CODE") or "something-7eleven-path-get-code"
)
THIRD_PARTY_7ELEVEN_PATH_GET_ONE_CODE = (
        os.environ.get("ELEVEN_PATH_GET_ONE_CODE") or "something-7eleven-path-get-one-code"
)

# VinId
THIRD_PARTY_VIN_ID_HOST = os.environ.get("VIN_ID_HOST") or "something-oneid-host"
THIRD_PARTY_VIN_ID_API_KEY = (
        os.environ.get("VIN_ID_API_KEY") or "something-oneid-api-key"
)
THIRD_PARTY_VIN_ID_POS_CODE = (
        os.environ.get("VIN_ID_POS_CODE") or "something-oneid-pos-code"
)
THIRD_PARTY_VIN_ID_STORE_CODE = (
        os.environ.get("VIN_ID_STORE_CODE") or "something-oneid-store-code"
)
THIRD_PARTY_VIN_ID_PATH_GET_CODE = (
        os.environ.get("VIN_ID_PATH_GET_CODE") or "something-oneid-path-get-code"
)
THIRD_PARTY_VIN_ID_PATH_GET_ONE_CODE = (
        os.environ.get("VIN_ID_PATH_GET_ONE_CODE") or "something-oneid-path-get-one-code"
)

# VTC
THIRD_PARTY_VTC_HOST = os.environ.get("VTC_URL_BASE") or "something-vtc-host"
THIRD_PARTY_VTC_PATH_BUY_CARD = (
        os.environ.get("VTC_PATH_BUY_CARD") or "something-vtc-path-buy-card"
)
THIRD_PARTY_VTC_PARTNER_CODE = (
        os.environ.get("VTC_PARTNER_CODE") or "something-vtc-partner-code"
)
THIRD_PARTY_VTC_BUY_CARD_CATEGORY = (
        os.environ.get("VTC_BUY_CARD_CATEGORY") or "something-vtc-buy-card-category"
)
THIRD_PARTY_VTC_TRIPLE_DES_KEY = (
        os.environ.get("VTC_TRIPLE_DES_KEY") or "something-vtc-triple-des-key"
)

# GiftPop
THIRD_PARTY_GIFT_POP_HOST = (
        os.environ.get("GIFT_POP_URL_BASE") or "something-gift-pop-host"
)
THIRD_PARTY_GIFT_POP_PATH_BUY_CODE = (
        os.environ.get("GIFT_POP_PATH_BUY_CODE") or "something-gift-pop-path-buy-code"
)
THIRD_PARTY_GIFT_POP_AUTH_KEY = (
        os.environ.get("GIFT_POP_AUTH_KEY") or "something-gift-pop-auth-key"
)
THIRD_PARTY_GIFT_POP_DECRYPT_KEY = (
        os.environ.get("GIFT_POP_DECRYPT_KEY") or "something-gift-pop-auth-key"
)
THIRD_PARTY_GIFT_POP_LANG = os.environ.get("GIFT_POP_LANG") or "something-gift-pop-lang"

# Urbox
THIRD_PARTY_URBOX_HOST = os.environ.get("URBOX_HOST") or "something-urbox-host"
THIRD_PARTY_URBOX_PATH_GET_CODE = (
        os.environ.get("URBOX_PATH_GET_CODE") or "something-urbox-path-get-code"
)

# VMG
THIRD_PARTY_VMG_HOST = os.environ.get("VMG_HOST") or "something-vmg-host"
THIRD_PARTY_VMG_USERNAME = os.environ.get("VMG_USERNAME") or "something-vmg-username"
THIRD_PARTY_VMG_PASSWORD = os.environ.get("VMG_PASSWORD") or "something-vmg-password"
THIRD_PARTY_VMG_KEY_BIRTHDAY_TIME = (
        os.environ.get("VMG_KEY_BIRTHDAY_TIME") or "something-vmg-key-birthday-time"
)
THIRD_PARTY_VMG_SOFT_PIN_KEY = (
        os.environ.get("VMG_SOFT_PIN_KEY") or "something-vmg-soft-pin-key"
)

# EZIN
THIRD_PARTY_EZIN_HOST = os.environ.get("EZIN_HOST") or "something-ezin-host"
THIRD_PARTY_EZIN_PATH_BUY_CODE = (os.environ.get("EZIN_PATH_BUY_CODE") or "something-ezin-path-buy-code")
THIRD_PARTY_EZIN_TOKEN = os.environ.get("EZIN_TOKEN") or "something-ezin-token"

# EZIN
THIRD_PARTY_SHOPEE_HOST = os.environ.get("SHOPEE_HOST") or "something-shopee-host"
THIRD_PARTY_SHOPEE_PATH_BUY_CODE = (os.environ.get("SHOPEE_PATH_BUY_CODE") or "something-shopee-path-buy-code")
THIRD_PARTY_SHOPEE_PARTNER_CODE = os.environ.get("SHOPEE_PARTNER_CODE") or "something-shopee-partner-code"
THIRD_PARTY_SHOPEE_PARTNER_SECRET = os.environ.get("SHOPEE_PARTNER_SECRET") or "something-shopee-partner-secret"
THIRD_PARTY_SHOPEE_PRIVKEYFORSHOPEE_PEM = os.environ.get("PRIVKEYFORSHOPEE_PEM") or "something-private-key-shopee"

# VietGuys
THIRD_PARTY_VIETGUYS_HOST = os.environ.get("VIETGUYS_HOST") or "something-host"
THIRD_PARTY_VIETGUYS_PATH_BUY_CODE = (os.environ.get("VIETGUYS_PATH_BUY_CODE") or "something-path-buy-code")
THIRD_PARTY_VIETGUYS_PATH_AUTHEN = (os.environ.get("VIETGUYS_PATH_AUTHEN") or "something-path-authen")
THIRD_PARTY_VIETGUYS_PARTNER_USERNAME = os.environ.get("VIETGUYS_PARTNER_USERNAME") or "something-partner-code"
THIRD_PARTY_VIETGUYS_ACCESS_TOKEN = os.environ.get("VIETGUYS_ACCESS_TOKEN") or "something-partner-secret"
THIRD_PARTY_VIETGUYS_SECRET_CODE = os.environ.get("VIETGUYS_SECRET_CODE") or "something-partner-secret"
THIRD_PARTY_VIETGUYS_NETWORK_VENDOR = os.environ.get("VIETGUYS_NETWORK_VENDOR") or "something-partner-secret"

# grab
GRAB_HOST = os.environ.get("GRAB_HOST") or "something-host"
GRAB_PATH_BUY_CODE = os.environ.get("GRAB_PATH_BUY_CODE") or "something-host"
GRAB_PATH_GIFT_LINKS = os.environ.get("GRAB_PATH_GIFT_LINKS") or "something-host"
GRAB_PATH_AUTH = os.environ.get("GRAB_PATH_AUTH") or "something-host"
GRAB_CLIENT_ID = os.environ.get("GRAB_CLIENT_ID") or "something-client-id"
GRAB_CLIENT_SECRET = os.environ.get("GRAB_CLIENT_SECRET") or "something-client-secret"
GRAB_GRANT_TYPE = os.environ.get("GRAB_GRANT_TYPE") or "something-grant-type"
GRAB_SCOPE = os.environ.get("GRAB_SCOPE") or "something-scope"

TELEGRAM_TOKEN = os.environ.get('TELEGRAM_TOKEN') or 'something'
TELEGRAM_CHAT_ID = os.environ.get('TELEGRAM_CHAT_ID') or 'something-else'

#ShopeeFood
THIRD_PARTY_SHOPEE_FOOD_HOST = os.environ.get("SHOPEE_FOOD_HOST") or "something-shopee-food-host"
THIRD_PARTY_SHOPEE_FOOD_PATH_BUY_CODE = (os.environ.get("SHOPEE_FOOD_PATH_BUY_CODE") or "something-shopee-food-path-buy-code")
THIRD_PARTY_SHOPEE_FOOD_APP_ID = os.environ.get("SHOPEE_FOOD_APP_ID") or "something-shopee-food-app-id"
THIRD_PARTY_SHOPEE_FOOD_APP_SECRET = os.environ.get("SHOPEE_FOOD_APP_SECRET") or "something-shopee-food-secret"

# VMG V2
THIRD_PARTY_VMG_HOST_V2 = os.environ.get("VMG_HOST_V2") or "something-vmg-host-v2"
THIRD_PARTY_VMG_USERNAME_V2 = os.environ.get("VMG_USERNAME_V2") or "something-vmg-username-v2"
THIRD_PARTY_VMG_PASSWORD_V2 = os.environ.get("VMG_PASSWORD_V2") or "something-vmg-password-v2"
THIRD_PARTY_VMG_KEY_BIRTHDAY_TIME_V2 = os.environ.get("VMG_KEY_BIRTHDAY_TIME_V2") or "something-vmg-key-birthday-time-v2"
THIRD_PARTY_VMG_SOFT_PIN_KEY_V2 =os.environ.get("VMG_SOFT_PIN_KEY_V2") or "something-vmg-soft-pin-key-v2"
THIRD_PARTY_VMG_PRIVATE_KEY_V2 = os.environ.get("VMG_PRIVATE_KEY_FROM_URBOX_V2") or "something-vmg-private-key-v2"
THIRD_PARTY_VMG_PUBLIC_KEY_TO_URBOX_V2 = os.environ.get("VMG_PUBLIC_KEY_TO_URBOX_V2") or "something-vmg-public-key-to-urbox-v2"
DEFINE_SUPPLIER_VMG_V2 = "vmg_v2"

# Appota
THIRD_PARTY_APPOTA_HOST = os.environ.get("APPOTA_HOST") or "https://api.appotapay.com"
THIRD_PARTY_APPOTA_SECRET_KEY = os.environ.get("APPOTA_SECRET_KEY") or "something-appota-secret-key"
THIRD_PARTY_APPOTA_PARTNER_CODE = os.environ.get("APPOTA_PARTNER_CODE") or "something-appota-partner-code"
THIRD_PARTY_APPOTA_API_KEY = os.environ.get("APPOTA_API_KEY") or "something-appota-api-key"
THIRD_PARTY_APPOTA_PRIVATE_KEY = os.environ.get("APPOTA_PRIVATE_KEY") or "something-appota-private-key"
DEFINE_SUPPLIER_APPOTA = "appota_v1"

# VietGuys V4
THIRD_PARTY_VIETGUYS_PATH_BUY_CODE_V4 = (os.environ.get("VIETGUYS_PATH_BUY_CODE_V4") or "something-path-buy-code-v4")
THIRD_PARTY_VIETGUYS_PATH_AUTHEN_V4 = (os.environ.get("VIETGUYS_PATH_AUTHEN_V4") or "something-path-authen-v4")
THIRD_PARTY_VIETGUYS_PARTNER_USERNAME_V4 = os.environ.get("VIETGUYS_PARTNER_USERNAME_V4") or "something-partner-code"
THIRD_PARTY_VIETGUYS_ACCESS_TOKEN_V4 = os.environ.get("VIETGUYS_ACCESS_TOKEN_V4") or "something-partner-secret"
THIRD_PARTY_VIETGUYS_NETWORK_VENDOR_V4 = os.environ.get("VIETGUYS_NETWORK_VENDOR_V4") or "something-partner-secret"
THIRD_PARTY_VIETGUYS_PRIVATE_KEY_FROM_UBOX_V4 = os.environ.get("VIETGUYS_PRIVATE_KEY_FROM_UBOX_V4") or "something-partner-secret"
THIRD_PARTY_VIETGUYS_PUBLIC_KEY_FROM_VGS_V4 = os.environ.get("VIETGUYS_PUBLIC_KEY_FROM_VGS_V4") or "something-partner-secret"
THIRD_PARTY_SUPPLIER_ID_VIETGUYS_V4 = os.environ.get("SUPPLIER_ID_VIETGUYS_V4") or "something-partner-secret"