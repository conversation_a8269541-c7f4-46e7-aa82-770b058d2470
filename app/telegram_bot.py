import os
import telegram
from jinja2 import Template

from app.const import TELEGRAM_CHAT_ID, TELEGRAM_TOKEN
from loguru import logger


def send_telegram_message_to_group(message="Lỗi không xác định A12", document=None):
    # Telegram has a 4096 character limit for messages
    MAX_MESSAGE_LENGTH = 4000  # Slightly less than 4096 to be safe

    try:
        # Check if message is too long and truncate if necessary
        if len(message) > MAX_MESSAGE_LENGTH:
            logger.warning(f"Telegram message too long ({len(message)} chars). Truncating to {MAX_MESSAGE_LENGTH} chars.")
            message = message[:MAX_MESSAGE_LENGTH] + "\n... (message truncated)"
        bot = telegram.Bot(token=TELEGRAM_TOKEN)
        # First send the text message
        message_response = bot.send_message(text=message, chat_id=int(TELEGRAM_CHAT_ID), parse_mode=telegram.ParseMode.MARKDOWN)

        # If there's a document, send it as a separate message
        if document is not None:
            document_response = bot.send_document(chat_id=int(TELEGRAM_CHAT_ID), document=document)
            # We prioritize returning the text message response for consistency
            # since that's what contains our actual message content

        return message_response
    except telegram.error.RetryAfter as e:
        # This exception is raised when Telegram rate limits us
        logger.warning(f"Telegram rate limit hit. Need to wait {e.retry_after} seconds.")
        # Return None to indicate rate limiting, which will trigger retry logic
        return None
    except telegram.error.BadRequest as e:
        # This could be due to malformed markdown or other issues
        logger.error(f"Telegram BadRequest error: {str(e)}")
        # Try sending without markdown parsing if it might be a markdown issue
        if "can't parse entities" in str(e).lower() or "markdown" in str(e).lower():
            try:
                logger.info("Retrying without markdown parsing")
                bot = telegram.Bot(token=TELEGRAM_TOKEN)
                message_response = bot.send_message(text=message, chat_id=int(TELEGRAM_CHAT_ID), parse_mode=None)

                # If there's a document, try to send it too
                if document is not None:
                    try:
                        document_response = bot.send_document(chat_id=int(TELEGRAM_CHAT_ID), document=document)
                    except Exception as doc_e:
                        logger.error(f"Failed to send document: {str(doc_e)}")

                return message_response
            except Exception as inner_e:
                logger.error(f"Failed to send even without markdown: {str(inner_e)}")
        return None
    except telegram.error.TelegramError as e:
        # Handle specific Telegram errors
        logger.error(f"Telegram API error: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error sending Telegram message: {str(e)}")
        logger.info(f"{TELEGRAM_TOKEN}, {TELEGRAM_CHAT_ID}, {message}")
        logger.error('Lỗi gửi tin nhắn telegram bot')
        return None


def build_message_get_code_order_template(params=None, isSuccess=True):
    if params is None:
        return ''
    template = get_code_order_template()
    if not isSuccess:
        template = get_code_order_error_template()
    return render_with_template(template, **params)


def get_code_order_template():
    return '''
{{icon}} [{{env}}]
```
OrderID: {{order_id}}
Tổng số code cần lấy: {{order_quantity}}
Tổng số code đã lấy: {{order_quantity_success}}
Product_id: {{product_id}}, Product_code: {{product_code}}, Supplier_id: {{supplier_id}}
```
'''


def get_code_order_error_template():
    return '''
🔴[{{env}}]
```
Kết quả order code - OrderID: {{order_id}} - Lỗi
- Mã giao dịch UrBox: {{transaction_id}}
- Mã giao dịch Đối tác: {{vendor_transaction_id}}
- OrderDetailId: {{order_detail_id}}
- Product_id: {{product_id}}
- Note: {{error_message}}
```
'''


def render_with_template(template, **kwargs):
    if len(template) == 0:
        return ''
    template = Template(template)
    return template.render(**kwargs)

def del_telegram_message(message_id):
    try:
        bot = telegram.Bot(token=TELEGRAM_TOKEN)
        bot.delete_message(chat_id=int(TELEGRAM_CHAT_ID), message_id=message_id)
        return True
    except telegram.error.BadRequest as e:
        # This happens if the message is already deleted or too old
        if "message to delete not found" in str(e).lower():
            logger.info(f"Message {message_id} already deleted or not found")
            return True  # Consider this a success since the message is gone
        logger.error(f"Telegram BadRequest when deleting message: {e}")
    except telegram.error.RetryAfter as e:
        # Rate limited when deleting
        logger.warning(f"Rate limited when deleting message. Need to wait {e.retry_after} seconds.")
    except telegram.error.TelegramError as e:
        # Other Telegram-specific errors
        logger.error(f"Telegram error when deleting message: {e}")
    except Exception as e:
        logger.error(f"Unexpected error deleting Telegram message: {e}")
        logger.error('Lỗi xóa tin nhắn telegram bot..')
    return False