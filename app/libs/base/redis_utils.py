#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json
from re import M

from loguru import logger

from app.const import (
    REDIS_CODEX_KEY,
    STATUS_ON,
    STATUS_OFF,
    REDIS_FLAG_PRODUCT,
    DAY,
    REDIS_FLAG_KAFKA,
    REDIS_KEY_STORE_CODE,
)
from app.extensions import redis_client
from app.repositories import product_flag_repo, codex_repo


class RedisUtility:
    @staticmethod
    def push_code_to_redis(codex):
        try:
            if not redis_client.ping():
                return

            redis_key = "{prefix}{product_id}".format(
                prefix=REDIS_KEY_STORE_CODE, product_id=codex.product_id
            )
            value = dict(
                id=codex.id,
                code=codex.codex,
                serial=codex.serial,
                pin=codex.pin,
                expired=codex.expired_time,
                is_update=codex.is_giftcode or 1,
            )
            if redis_client.lpush(redis_key, json.dumps(value)):
                codex_repo.mark_code_as_to_redis(codex.id)
        except Exception as e:
            logger.exception(e)

    @staticmethod
    def get_kafka_flag():
        try:
            if not redis_client.ping():
                return STATUS_OFF

            flag = redis_client.get(REDIS_FLAG_KAFKA)
            if flag is None or int(flag) == 0:
                return STATUS_ON
            return STATUS_OFF
        except Exception as e:
            logger.exception(e)
            return STATUS_OFF

    @staticmethod
    def get_product_flag(product_id):
        try:
            if not redis_client.ping():
                return product_flag_repo.get_flag(product_id)

            redis_key = "{prefix}:{product_id}".format(
                prefix=REDIS_FLAG_PRODUCT, product_id=product_id
            )
            flag = redis_client.get(redis_key)
            if flag is None:
                flag = product_flag_repo.get_flag(product_id)
                RedisUtility.set_product_flag_to_redis(product_id, flag)
            return int(flag)
        except Exception as e:
            logger.exception(e)
            return STATUS_OFF

    @staticmethod
    def set_product_flag_to_redis(product_id, flag):
        try:
            if not redis_client.ping():
                return

            redis_key = "{prefix}:{product_id}".format(
                prefix=REDIS_FLAG_PRODUCT, product_id=product_id
            )
            redis_client.set(redis_key, flag)
            redis_client.expire(redis_key, 60)
        except Exception as e:
            logger.exception(e)

    @staticmethod
    def turn_on_product_flag(product_id):
        flag = product_flag_repo.get_product_flag(product_id)
        if not flag:
            product_flag_repo.create(product_id)
        else:
            flag.turn_on_flag()
        RedisUtility.set_product_flag_to_redis(product_id, STATUS_ON)

    @staticmethod
    def turn_off_product_flag(product_id):
        flag = product_flag_repo.get_product_flag(product_id)
        if flag:
            flag.turn_off_flag()
        RedisUtility.set_product_flag_to_redis(product_id, STATUS_OFF)

    @staticmethod
    def clear_codex_on_redis_by_product_id(product_id):
        try:
            if not redis_client.ping():
                return
            redis_key = "{prefix}:{product_id}".format(
                prefix=REDIS_CODEX_KEY, product_id=product_id
            )
            redis_client.delete(redis_key)
            return True
        except Exception as e:
            logger.exception(e)
            return False
