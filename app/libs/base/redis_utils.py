#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json

from loguru import logger

from app.const import (
    STATUS_ON,
    STATUS_OFF,
    REDIS_FLAG_PRODUCT,
    DAY,
    REDIS_FLAG_KAFKA
)
from app.extensions import redis_client
from app.repositories import product_flag_repo, codex_repo


class RedisUtility:
    @staticmethod
    def get_kafka_flag():
        try:
            if not redis_client.ping():
                return STATUS_OFF

            flag = redis_client.get(REDIS_FLAG_KAFKA)
            if flag is None or int(flag) == 0:
                return STATUS_ON
            return STATUS_OFF
        except Exception as e:
            logger.exception(e)
            return STATUS_OFF

    @staticmethod
    def get_product_flag(product_id):
        try:
            if not redis_client.ping():
                return product_flag_repo.get_flag(product_id)

            redis_key = "{prefix}:{product_id}".format(
                prefix=REDIS_FLAG_PRODUCT, product_id=product_id
            )
            flag = redis_client.get(redis_key)
            if flag is None:
                flag = product_flag_repo.get_flag(product_id)
                RedisUtility.set_product_flag_to_redis(product_id, flag)
            return int(flag)
        except Exception as e:
            logger.exception(e)
            return STATUS_OFF

    @staticmethod
    def set_product_flag_to_redis(product_id, flag):
        try:
            if not redis_client.ping():
                return

            redis_key = "{prefix}:{product_id}".format(
                prefix=REDIS_FLAG_PRODUCT, product_id=product_id
            )
            redis_client.set(redis_key, flag)
            redis_client.expire(redis_key, 15 * DAY)
        except Exception as e:
            logger.exception(e)
