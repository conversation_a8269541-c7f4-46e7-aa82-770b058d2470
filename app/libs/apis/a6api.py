#!/usr/bin/python
# -*- coding: utf-8 -*-
from app.const import (
    SERVICE_DOMAIN_A6_PRODUCT,
    SERVICE_ACTION_A6_PRODUCT_NEED_GET_CODE,
    SERVICE_VERSION_A6_PRODUCT,
    SERVICE_ACTION_A6_PRODUCT_UPDATE,
    SERVICE_ACTION_A6_PRODUCT_GET,
    SERVICE_ACTION_A6_PRODUCT_SUPPLIER,
)
from app.libs.base.request_dao import RequestDao
from app.libs.apis.abstract_aapi import AbstractAApi
from loguru import logger

request_dao = RequestDao()


class A6Api(AbstractAApi):
    @staticmethod
    def get_product_need_get_code():
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A6_PRODUCT,
            version=SERVICE_VERSION_A6_PRODUCT,
            action=SERVICE_ACTION_A6_PRODUCT_NEED_GET_CODE,
        )
        headers = A6Api.headers()
        query_params = A6Api.add_query_params(dict())
        response = request_dao.get(url, params=query_params, headers=headers)
        if A6Api.is_response_success(response):
            return response.get("data")
        return None

    @staticmethod
    def update_quantity_stock_of_code(*args, **kwargs):
        product_id = (
            kwargs.get("product_id") if kwargs.get("product_id") is not None else None
        )
        quantity = (
            kwargs.get("quantity") if kwargs.get("quantity") is not None else None
        )

        if product_id is None:
            return

        if quantity is None or quantity == 0:
            return

        query_params = dict(id=product_id, quantity_stock_added=quantity)
        headers = A6Api.headers()
        query_params = A6Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A6_PRODUCT,
            version=SERVICE_VERSION_A6_PRODUCT,
            action=SERVICE_ACTION_A6_PRODUCT_UPDATE,
        )
        response = request_dao.put(url, params=query_params, headers=headers)
        if A6Api.is_response_success(response):
            return response.get("data")
        return None

    @staticmethod
    def get_product_codex_by_id(*args, **kwargs):
        query_params = dict(ids=kwargs.get("product_id"))
        query_params = A6Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A6_PRODUCT,
            version=SERVICE_VERSION_A6_PRODUCT,
            action=SERVICE_ACTION_A6_PRODUCT_UPDATE,
        )
        headers = A6Api.headers()
        response = request_dao.get(url, params=query_params, headers=headers)
        if A6Api.is_response_success(response):
            return response.get("data")
        return None

    @staticmethod
    def get_suppliers(product_id):
        url = "{domain}/{version}/{action}/{product_id}/{type}".format(
            domain=SERVICE_DOMAIN_A6_PRODUCT,
            version=SERVICE_VERSION_A6_PRODUCT,
            action=SERVICE_ACTION_A6_PRODUCT_GET,
            product_id=product_id,
            type=SERVICE_ACTION_A6_PRODUCT_SUPPLIER,
        )
        query_params = A6Api.add_query_params(dict())
        logger.info(f'request A6 {url}: {query_params}')
        headers = A6Api.headers()
        response = request_dao.get_with_retry(url, params=query_params, headers=headers, retry=5, backoff_factor=0.5)
        if A6Api.is_response_success(response):
            return response.get("data")
        return None

    @staticmethod
    def get_product_info(product_id):
        url = "{domain}/{version}/{action}/{product_id}".format(
            domain=SERVICE_DOMAIN_A6_PRODUCT,
            version=SERVICE_VERSION_A6_PRODUCT,
            action=SERVICE_ACTION_A6_PRODUCT_GET,
            product_id=product_id,
        )
        query_params = A6Api.add_query_params(dict())
        headers = A6Api.headers()
        response = request_dao.get(url, params=query_params, headers=headers)
        if A6Api.is_response_success(response):
            return response.get("data")
        return None
