# coding: utf8

from loguru import logger

import pytz
import datetime
import uuid

from app.const import LOCAL_TIME_ZONE, STATUS_ON, PROCESS_SENT
from app.helper import Helper
from app.libs.apis.a21api import A21Api
from app.models.mongodb import LogsRequestApi
from app.repositories import codex_repo, order_detail_repo
from app.security import UrboxTripleDes
from app.libs.base.utility import Utility


class AbstractApi:
    def __init__(self, *args, **kwargs):
        pass

    def format_code(self, *args, **kwargs):
        serial = kwargs.get("serial") if kwargs.get("serial") is not None else None
        codex = kwargs.get("codex") if kwargs.get("codex") is not None else None
        pin = kwargs.get("pin") if kwargs.get("pin") is not None else None
        expired = kwargs.get("expired") if kwargs.get("expired") is not None else None
        partner_codex_id = kwargs.get("partner_codex_id") if kwargs.get("partner_codex_id") is not None else None

        codex = UrboxTripleDes.encode(codex)
        codex_int = Utility.string_folding(codex)
        return dict(
            serial=serial,
            pin=pin,
            codex=codex,
            codex_int=codex_int,
            expired=expired,
            partner_codex_id=partner_codex_id
        )

    def create_logs(self, *args, **kwargs):
        try:
            transaction_id = kwargs.get("transaction_id")
            merchant_name = kwargs.get("merchant_name")
            request_url = kwargs.get("url")
            headers = kwargs.get("headers")
            data = kwargs.get("data")
            request_round = kwargs.get("request_round") or None

            if transaction_id is None:
                raise Exception("transaction_id is not found")
            if merchant_name is None:
                raise Exception("merchant_name is not found")
            if headers is None:
                raise Exception("headers is not found")
            if request_url is None:
                raise Exception("request_url is not found")
            if data is None:
                raise Exception("data is not found")

            request_headers = dict(request_headers=headers)

            request_data = dict(request_data=data)

            log = LogsRequestApi(
                transaction_id=transaction_id,
                merchant_name=merchant_name,
                request_url=request_url,
                request_headers=request_headers,
                request_data=request_data,
                request_round=request_round,
                contract_code=kwargs.get("contract_code") or ''
            )
            log.save()
            return log
        except Exception as e:
            logger.exception(e)
            return None

    def update_logs(self, *args, **kwargs):
        try:
            log = kwargs.get("log")
            headers = kwargs.get("headers")
            response_code = kwargs.get("response_code")
            data = kwargs.get("data")

            if log is None:
                raise Exception("log is not found")
            if headers is None:
                raise Exception("headers is not found")
            if response_code is None:
                raise Exception("response_code is not found")
            if data is None:
                raise Exception("data is not found")

            response_headers = dict(response_headers=headers)

            response_data = dict(response_data=data)

            log.response_headers = response_headers
            log.response_code = response_code
            log.response_data = response_data
            log.save()
            return log
        except Exception as e:
            logger.exception(e)
            return None

    def create_transaction_id(self, prefix="UB"):
        time_code = datetime.datetime.now(pytz.timezone(LOCAL_TIME_ZONE)).strftime(
            "%Y%m%d"
        )
        return f"{prefix}{time_code}{str(uuid.uuid4().node)}"

    def validate_get_code(self, product_info):
        if product_info.get("quantity") < 1:
            raise Exception("Số lượng tối thiểu phải là 1.")

        if product_info.get("quantity") > 2000:
            raise Exception("Số lượng không được vượt quá 2000.")

        if (
                not product_info.get("product_parent_id")
                or product_info.get("product_parent_id") == 0
        ):
            raise Exception("Sản phẩm không có product_parent_id")

    def validate_api_config(self, *args, **kwargs):

        for param in kwargs:
            if kwargs.get(param) is None or kwargs.get(param) == "":
                raise Exception(param + " chưa được cấu hình")

    def before_get_code(self, prefix="UB"):
        return self.create_transaction_id(prefix)

    def after_get_code(self):
        pass

    def create_order_log(
            self, product_supplier_id, transaction_id, supplier_id, quantity, product_id, po_code = ''
    ):
        order_id = A21Api.create_order_log(
            product_supplier_id=product_supplier_id,
            transaction_id=transaction_id,
            supplier_id=supplier_id,
            quantity=quantity,
            product_id=product_id,
            po_code=po_code or ''
        )
        # TODO: Lưu mongo nếu order_id = False
        return order_id

    def update_order_log(self, order_id, quantity_success, quantity_error):
        # TODO: Nếu order_id = False (create_order_log không thành công) thì lưu luôn vào mongo
        response = A21Api.update_order_log(
            id=order_id,
            quantity_success=quantity_success,
            quantity_error=quantity_error,
            process=3 if quantity_success > 0 else 4,
        )
        # TODO: Lưu mongo nếu result = False

    def creater_order_detail_log(self, codes, order_id, product_id, money, product_code, get_transaction_id='',
                                 response_transaction_id='', po_code = ''):
        # TODO: Nếu order_id = False (create_order_log không thành công) thì lưu luôn vào mongo
        if len(codes) > 0:
            request_data = []
            for code in codes:
                data = dict(
                    supplier_order_id=order_id or 0,
                    gift_detail_id=product_id or 0,
                    codex=code.get("codex") or "",
                    expired=code.get("expired") or 0,
                    serial=code.get("serial") or "",
                    money=int(money) or 0,
                    process=2,
                    isPayed=1,
                    product_code=product_code or "",
                    get_transaction_id=get_transaction_id or "",
                    response_transaction_id=response_transaction_id or "",
                    po=po_code
                )
                request_data.append(data)
            response = A21Api.create_order_detail_log(request_data)
            # TODO: Lưu mongo nếu response = False
        else:
            Utility.send_telegram_message_to_group(
                message=f"Product {product_id}-{product_code} không lấy được code từ API. OrderId: {order_id}"
            )
            logger.error({
                "message": f"Không lấy được code từ API. OrderId: {order_id}",
                "product_id": product_id,
                "product_code": product_code,
                "supplier_order_id": order_id
            })

    def unique_codex_by_product_parent_id(self, list_codex, product_parent_id):
        list_unique_codex = []
        list_plain_codex_int = []
        if not list_codex:
            return list_unique_codex
        for codex in list_codex:
            if codex:
                list_plain_codex_int.append(str(codex.get("codex_int")))
        list_codex_exist = self.get_duplicate_codex_int_by_product_parent_id(list_plain_codex_int, product_parent_id)
        for codex in list_codex:
            if codex.get("codex") not in list_codex_exist:
                list_unique_codex.append(codex)
        return list_unique_codex

    # def unique_codex_by_product_parent_id(self, list_codex, product_parent_id):
    #     list_plain_codex = [codex.get("codex") for codex in list_codex]
    #     list_codex_exist = self.get_duplicate_codex_by_product_parent_id(list_plain_codex, product_parent_id)
    #     list_unique_codex = []
    #     for codex in list_codex:
    #         if codex.get("codex") not in list_codex_exist:
    #             list_unique_codex.append(codex)
    #     return list_unique_codex
    #
    # def get_duplicate_codex_by_product_parent_id(self, list_codex, product_parent_id):
    #     list_codex_exist = codex_repo.get_by_codex_and_product_parent_id(list_codex, product_parent_id)
    #     list_plain_codex_exist = []
    #     for codex in list_codex_exist:
    #         list_plain_codex_exist.append(codex.codex)
    #     return list_plain_codex_exist

    def get_duplicate_codex_int_by_product_parent_id(self, list_codex_int, product_parent_id):
        list_codex_exist = codex_repo.get_by_codex_int_and_product_parent_id(list_codex_int, product_parent_id)
        list_plain_codex_exist = []
        for codex in list_codex_exist:
            list_plain_codex_exist.append(codex.codex)
        return list_plain_codex_exist


    def save_order_detail_log(self, *args, **kwargs):
        order_detail_repo.create(**{
            "order_id": kwargs.get("order_id") or 0,
            "supplier_order_id": kwargs.get("supplier_order_id") or 0,
            "order_code": kwargs.get("order_code") or "",
            "request": kwargs.get("request") or '',
            "response": kwargs.get("response") or '',
            "transaction_id": kwargs.get("transaction_id") or '',
            "callback_body": kwargs.get("callback_body") or '',
            "header": kwargs.get("header") or '',
            "request_url": kwargs.get("request_url") or '',
            "http_code": kwargs.get("http_code") or '',
            "quantity": kwargs.get("quantity") or '',
            "status": kwargs.get("status") or STATUS_ON,
            "action": kwargs.get("action") or PROCESS_SENT,
            "created": Helper.get_now_unix_timestamp(),
            "updated": Helper.get_now_unix_timestamp(),
        })
        return True