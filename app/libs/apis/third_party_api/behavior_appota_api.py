# coding: utf8
import json
import time

from loguru import logger
import requests
import redis
import jwt


#https://docs.appotapay.com/buy-card/error-code
from app.const import (
    THIRD_PARTY_APPOTA_API_KEY,
    THIRD_PARTY_APPOTA_HOST,
    THIRD_PARTY_APPOTA_PARTNER_CODE,    
    THIRD_PARTY_APPOTA_PRIVATE_KEY,
    THIRD_PARTY_APPOTA_SECRET_KEY
)
from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import Behavior<PERSON>ode<PERSON><PERSON>
from app.security import UrBoxHash, UrboxOpenSsl
from app.utils import set_rate_limit_per_minute

class BehaviorAppotaApi(AbstractApi, BehaviorCodeApi):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "AppotaApi"
        self.private_key = THIRD_PARTY_APPOTA_PRIVATE_KEY
        self.api_key = THIRD_PARTY_APPOTA_API_KEY
        self.secret_key = THIRD_PARTY_APPOTA_SECRET_KEY
        self.partner_code = THIRD_PARTY_APPOTA_PARTNER_CODE
        self.api_get_code = THIRD_PARTY_APPOTA_HOST + "/api/v1/service/shopcard/buy"
        self.api_retry_get_code = THIRD_PARTY_APPOTA_HOST + "/api/v1/service/shopcard/transaction"
        self.api_get_balance = THIRD_PARTY_APPOTA_HOST + "/api/v1/service/accounts/balance"
        self.code_type = "normal"
        self.RATE_LIMIT = 500
        if THIRD_PARTY_APPOTA_HOST is None:
            raise Exception("Host chưa được cấu hình")
        if self.private_key is None:
            raise Exception("PrivateKey chưa được cấu hình")
        
        
    def get_headers(self):
        token = self.get_jwt_token()
        logger.info(f'token: {token}')
        return {
            "X-APPOTAPAY-AUTH": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
    def get_jwt_token(self):
        headers = {
             "typ": "JWT",
             "alg": "HS256",
             "cty": "appotapay-api;v=1"
        }
        payload = {
            "iss": self.partner_code,
            "jti": self.api_key + "-" + str(int(time.time())),
            "api_key": self.api_key,
            "exp": int(time.time()) + 1800
        }
        return jwt.encode(payload, self.secret_key, algorithm="HS256", headers=headers)
    
    
    def get_signature(self, partnerRefId, productCode, quantity):
        signature_string = f"partnerRefId={partnerRefId}&productCode={productCode}&quantity={quantity}&type={self.code_type}"
        return UrBoxHash.hmac_sha256(self.secret_key, signature_string)
    
    def get_code(self, product_info):
        set_rate_limit_per_minute(product_info.get("product_id"), self.RATE_LIMIT)
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        product_code = product_info.get("product_code")
        retry_transaction_id = product_info.get("retry_transaction_id")
        supplier_order_id = product_info.get("supplier_order_id")
        product_parent_id = product_info.get("product_parent_id")
        product_supplier_id = product_info.get("product_supplier_id")
        supplier_id = product_info.get("supplier_id")
        
        transaction_id = self.before_get_code()
        get_code_params = {
            "partnerRefId": transaction_id,
            "productCode": product_code,
            "type": self.code_type,
            "quantity": quantity,
            "signature": self.get_signature(transaction_id, product_code, quantity)
        }
        headers = self.get_headers()
        
        products = []
        
        if not product_parent_id or product_parent_id == 0:
            return products, {"transaction_id": transaction_id, "message": "Không tìm thấy product_parent_id"}
        if retry_transaction_id != '':
            transaction_id = retry_transaction_id
        else:
            supplier_order_id = self.create_order_log(
                product_supplier_id,
                transaction_id,
                supplier_id,
                quantity,
                product_id,
                product_info.get("po_code") or ''
            )
        message = ''
        
        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=self.api_get_code,
            headers=headers,
            data=get_code_params,
        )
        try:
            response = requests.post(self.api_get_code, headers=headers, data=json.dumps(get_code_params))
            response_json = response.json()
   
            self.update_logs(
                log=log,
                headers={},
                response_code=response.status_code or 0,
                data= json.loads(response.text) or {},
            )
            message = response_json.get("message")
            if response_json.get("errorCode") == 0 and "cards" in response_json:
                for product in response_json.get("cards"):
                    logger.info(f'product: {product}')
                    code = self.get_info_product(product)
                    products.append(code)
                products = self.unique_codex_by_product_parent_id(
                    products, product_parent_id
                )
                message = ''
            
        except Exception as e:
            logger.exception(e)
            if log:
                self.update_logs(
                    log=log,
                    response_code=500,
                    headers={},
                    data= {
                        "error": str(e),
                    }
                )
            message = str(e) if str(e) else "Lỗi không xác định"
        quantity_success = len(products)
        quantity_error = quantity - quantity_success
        logger.info(f'transaction_id: {transaction_id}, product_id: {product_id}, quantity_success: {quantity_success}, quantity_error: {quantity_error}')
        logger.info(f'transaction_id: {transaction_id}, products: {products}')
        self.update_order_log(supplier_order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(
            products, supplier_order_id, product_id, price, product_code,transaction_id
        )
        return products, {"transaction_id": transaction_id, "message": message}


    def get_info_product(self, product):
        try:
            expired = product.get("expiry")
            serial = product.get("serial")
            code = product.get("code")
            codex = UrboxOpenSsl.openssl_decrypt(self.private_key, code)
            logger.info(f'expired: {expired}, serial: {serial}, code: {code}, codex: {codex}')
            expired = Utility.convert_datetime_to_unixtimestamp(
                expired, "%d-%m-%Y"
            )
            return self.format_code(
                serial=serial,
                pin=None,
                codex=codex,
                expired=expired + 86399,
                partner_codex_id = ""
            )
        except Exception as e:
            logger.exception(e)
        return None

    def get_balance(self):
        try:
            headers = self.get_headers()
            response = requests.get(self.api_get_balance, headers=headers)
            logger.info(f'response: {response.text}')
            response_json = response.json()
            return response_json
        except Exception as e:
            logger.exception(e)
        return {
            "message": str(e),
            "error_code": 500
        }