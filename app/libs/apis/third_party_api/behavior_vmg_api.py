# coding: utf8
import json
import time

from loguru import logger
from zeep import Client, helpers, Settings
from zeep.transports import Transport
from requests import Session

import re

from app.const import (
    THIRD_PARTY_VMG_HOST,
    THIRD_PARTY_VMG_PASSWORD,
    THIRD_PARTY_VMG_KEY_BIRTHDAY_TIME,
    THIRD_PARTY_VMG_SOFT_PIN_KEY,
    THIRD_PARTY_VMG_USERNAME,
)
from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import Behavior<PERSON>odeApi
from app.libs.apis.third_party_api.soap_schemas import create_soap_transport, create_soap_settings
from app.security import VmgTripleDes


class BehaviorVmgApi(AbstractApi, BehaviorCodeApi):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "VmgApi"
        self.host = THIRD_PARTY_VMG_HOST
        self.username = THIRD_PARTY_VMG_USERNAME
        self.password = THIRD_PARTY_VMG_PASSWORD
        self.key_birthday_time = THIRD_PARTY_VMG_KEY_BIRTHDAY_TIME
        self.soft_pin_key = THIRD_PARTY_VMG_SOFT_PIN_KEY

        if self.host is None:
            raise Exception("Host chưa được cấu hình")
        if self.username is None:
            raise Exception("Username chưa được cấu hình")
        if self.password is None:
            raise Exception("Password chưa được cấu hình")
        if self.key_birthday_time is None:
            raise Exception("KeyBirthdayTime chưa được cấu hình")
        if self.soft_pin_key is None:
            raise Exception("SoftPinKey chưa được cấu hình")

    def get_code(self, product_info):
        logger.info(product_info)
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        product_code = product_info.get("product_code")
        retry_transaction_id = product_info.get("retry_transaction_id")
        supplier_order_id = product_info.get("supplier_order_id")
        codes = []

        token = self.sign_in_as_partner()
        if token is None:
            logger.info('VMG - Không đăng nhập được vào hệ thống')
            return codes, {"transaction_id": None, "message": "Không đăng nhập được vào hệ thống"}

        codes, transaction_id  = self.call_api_get_code(
            product_info.get("product_supplier_id"),
            product_info.get("supplier_id"),
            product_id,
            product_code,
            quantity,
            price,
            token,
            product_info.get("product_parent_id"),
            retry_transaction_id,
            supplier_order_id,
            product_info.get("contract_code")
        )
        return codes, {"transaction_id": transaction_id, "message": "Lấy mã thành công" if codes else "Không lấy được mã"}

    def call_api_get_code(
            self,
            product_supplier_id,
            supplier_id,
            product_id,
            product_code,
            quantity,
            price,
            token,
            product_parent_id,
            retry_transaction_id,
            supplier_order_id,
            contract_code
    ):
        products = []
        if not product_parent_id or product_parent_id == 0:
            return products, None
        transaction_id = self.before_get_code()
        if retry_transaction_id != '':
            transaction_id = retry_transaction_id
        else:
            supplier_order_id = self.create_order_log(
                product_supplier_id, transaction_id, supplier_id, quantity, product_id
            )
        try:
            # Sử dụng các hàm tiện ích để tạo transport và settings
            transport = create_soap_transport()
            settings = create_soap_settings()

            client = Client(wsdl=self.host, settings=settings, transport=transport)
            # array_of_buy_item = client.get_type('ns0:BuyItem')
            params = self.get_params_get_code(product_code, quantity)
            serialized_params = helpers.serialize_object(params)

            log = self.create_logs(
                transaction_id=transaction_id,
                merchant_name=self.title,
                url=self.host,
                headers={},
                data=json.loads(json.dumps(serialized_params)),
                contract_code=contract_code
            )
            logger.info(f'transaction_id: {transaction_id}, product_id: {product_id}')
            if retry_transaction_id != '':
                response = client.service.partnerRedownloadSoftpin(
                    self.username, retry_transaction_id, self.key_birthday_time, token
                )
            else:
                response = client.service.partnerDownloadSoftpinV10(
                    self.username, params, transaction_id, self.key_birthday_time, token
                )
            response_serialized = helpers.serialize_object(response)
            self.update_logs(
                log=log,
                headers={},
                response_code=response.errorCode,
                data=json.loads(json.dumps(response_serialized)),
            )
            if response.errorCode == 0 and "products" in response:
                # contents = helpers.serialize_object(response)
                products = []
                for product in response.products:
                    # money = product.productValue
                    if "softpins" in product:
                        for item in product.softpins:
                            code = self.get_info_product(item)
                            products.append(code)
                products = self.unique_codex_by_product_parent_id(
                    products, product_parent_id
                )
            else:
                logger.info(f'transaction_id: {transaction_id},  - response: {response}')
        except Exception as e:
            logger.exception(e)
        quantity_success = len(products)
        quantity_error = quantity - quantity_success
        self.update_order_log(supplier_order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(
            products, supplier_order_id, product_id, price, product_code,transaction_id
        )
        return products, transaction_id

    def get_info_product(self, product):
        try:
            serialize_item = helpers.serialize_object(product)
            try:
                vmg_expired = serialize_item["expiryDate"]
            except Exception as e:
                vmg_expired = serialize_item["expiryDate"]["_value_1"]
            try:
                vmg_soft_pin_serial = serialize_item["softpinSerial"]
            except Exception as e:
                vmg_soft_pin_serial = serialize_item["softpinSerial"]["_value_1"]

            try:
                vmg_soft_pin_code = serialize_item["softpinPinCode"]
            except Exception as e:
                vmg_soft_pin_code = serialize_item["softpinPinCode"]["_value_1"]

            download_soft_pin_key = str(self.soft_pin_key[0:8])
            vmg_codex = VmgTripleDes.decode(
                vmg_soft_pin_code, self.soft_pin_key, download_soft_pin_key
            )
            vmg_codex = re.sub("[^A-Za-z0-9]+", "", vmg_codex)

            expired = Utility.convert_datetime_to_unixtimestamp(
                vmg_expired, "%Y/%m/%d %H:%M:%S"
            )
            return self.format_code(
                serial=vmg_soft_pin_serial,
                pin=None,
                codex=vmg_codex,
                expired=expired,
            )
        except Exception as e:
            logger.exception(e)
        return None

    def sign_in_as_partner(self):
        try:
            # Sử dụng các hàm tiện ích để tạo transport và settings
            transport = create_soap_transport()
            settings = create_soap_settings()

            client = Client(wsdl=self.host, settings=settings, transport=transport)
            response = client.service.signInAsPartner(self.username, self.password)
            # contents = helpers.serialize_object(response)
            if response.errorCode == 0:
                return response.token
            logger.info(response.text)
        except Exception as e:
            logger.info(f'login-response: {str(e)}')
            logger.exception(e)
        return None

    def get_params_get_code(self, product_code, quantity):
        # Sử dụng các hàm tiện ích để tạo transport và settings
        transport = create_soap_transport()
        settings = create_soap_settings()

        client = Client(wsdl=self.host, settings=settings, transport=transport)
        factory = client.type_factory("ns0")
        array_of_buy_item = factory.ArrayOfBuyItem
        buy_item = factory.BuyItem
        arguments = array_of_buy_item([buy_item(product_code, quantity)])
        return arguments

    def get_limit_code(self):
        pass
