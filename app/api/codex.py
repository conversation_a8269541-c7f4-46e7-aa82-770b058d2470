#!/usr/bin/env python
# -*- coding: utf-8 -*-

from loguru import logger
from werkzeug.exceptions import BadRequest

from app.config import config_app
from app.const import MAXIMUM_PRODUCT_REQUEST, MAXIMUM_PRODUCT_QUANTITY, LIMIT_NUMBER_OF_CODEX_ORDER
from app.decorators import validate_func
from app.extensions import kafka_producer
from app.inum import CodexUpdateCase
from app.models.mongodb import LogsRequestApi
from app.repositories import codex_repo
from app.services.code import CodeDao
from app.services.webhook import Webhook


class CodexService(object):

    @classmethod
    @validate_func(
        **{
            "type": "object",
            "properties": {
                "products": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "product_id": {"type": ["string", "integer"]},
                            "quantity": {"type": "integer"},
                        },
                    },
                    "required": ["product_id", "quantity"],
                },
            },
            "required": ["products"],
        }
    )
    def get(cls, args, **kwargs):
        if cls.__validate_before_get(args) is False:
            return False
        code = CodeDao()
        response = code.get_new(args)

        return response

    @classmethod
    def __validate_before_get(cls, args):
        if len(args.get("products")) > MAXIMUM_PRODUCT_REQUEST:
            return False

        for product in args.get("products"):
            if (product.get("product_id") is None
                    or product.get("quantity") is None
                    or product.get("quantity") > MAXIMUM_PRODUCT_QUANTITY):
                return False
        return True

    @classmethod
    @validate_func(
        **{
            "type": "object",
            "properties": {
                "request_id": {"type": "string"},
                "status": {"type": "integer"},
                "detail": {
                    "type": "object",
                    "properties": {
                        "timestamp": {"type": "integer"},
                        "amount": {"type": "integer"},
                        "codex": {
                            "type": "array",
                            "properties": {
                                "code": {"type": "string"},
                                "expired": {"type": "integer"},
                                "serial": {"type": "string"},
                                "pin": {"type": "string"},
                            },
                        },
                    },
                },
            },
        }
    )
    def webhook(cls, args, **kwargs):
        code = Webhook()
        response = code.webhook(**kwargs)

        return response

    @classmethod
    @validate_func(
        **{
            "type": "object",
            "properties": {
                "error": {"type": "integer", "default": 1},
                "message": {"type": "string"},
                "data": {
                    "type": "object",
                    "properties": {
                        "transaction_id": {"type": "string"},
                        "tracking_id": {"type": "string"},
                        "timestamp": {"type": "integer"},
                        "telco": {"type": "string"},
                        "type": {"type": "string"},
                        "amount": {"type": "integer"},
                        "api_version": {"type": "string"},
                        "cards": {
                            "type": "array",
                            "properties": {
                                "seri": {"type": "string"},
                                "code": {"type": "string"},
                                "expired_at": {"type": "string"}
                            },
                        },
                        "iv": {"type": "string"},
                    },
                },
            },
        }
    )
    def vietguys_webhook(cls, args, **kwargs):
        data = kwargs.get('data')
        log = LogsRequestApi(
            transaction_id=data.get("tracking_id"),
            merchant_name="VietGuysWebhook",
            request_url=f"/v1/ura12/webhook/vgs/{kwargs.get('contract_signed_with')}",
            request_data= dict(request_data=kwargs),
            request_headers=None,
        )
        log.save()

        code = Webhook()
        response = code.vgs_webhook(**kwargs)
        return response

    @classmethod
    @validate_func(
        **{
            "type": "object",
            "properties": {
                "txType": {"type": "string"},
                "partnerReference": {"type": "string"},
                "txID": {
                    "type": "string",
                },
                "txStatus": {
                    "type": "string",
                },
                "meta": {
                    "type": "object",
                    "properties": {
                        # "error": {"type": "object"},
                        # "cancel": {
                        #     "type": "object",
                        #     "properties": {
                        #         "cancelled": {"type": "string"},
                        #         "failed": {"type": "string"},
                        #     },
                        # },
                        # "create": {
                        #     "type": "object",
                        #     "properties": {
                        #         "giftsCreated": {"type": "integer"},
                        #     },
                        # },
                    },
                },
                "createdAt": {"type": "integer"},
                "completedAt": {"type": "integer"},
            },
        }
    )
    def grab_webhook(cls, args, **kwargs):
        log = LogsRequestApi(
            transaction_id=kwargs.get("partnerReference"),
            merchant_name="GrabWebhook",
            request_url="/v1/ura12/webhook/grab",
            request_data=dict(request_data=kwargs),
            request_headers=None,
        )
        log.save()

        code = Webhook()
        response = code.grab_webhook(**kwargs)
        return response

    @classmethod
    @validate_func(
        **{
            "type": "object",
            "properties": {
                "quantity": {"type": "integer"},
                "product_id": {"type": "integer"},
                "product_supplier_id": {"type": "integer"},
                "start_date": {"type": "integer"},
                "end_date": {"type": "integer"},
            },
            "required": ["quantity", "product_supplier_id", "product_id"],
        }
    )
    def get_code_by_order(cls, args, **kwargs):
        if len(cls.__validate_get_by_order(args)) > 0:
            raise BadRequest(
                description=cls.__validate_get_by_order(args)
            )
        return
        #chuyển phần này sang portal rồi
        # code = CodeDao()
        # response = code.get_code_by_order(**kwargs)
        # return response

    @classmethod
    def __validate_get_by_order(cls, args):
        if args.get('quantity') > LIMIT_NUMBER_OF_CODEX_ORDER:
            return f'Maximum Quantity of {LIMIT_NUMBER_OF_CODEX_ORDER}'
        return ""

    @classmethod
    @validate_func(
        **{
            "type": "object",
            "properties": {
                "products": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "product_id": {"type": "integer"},
                            "quantity": {"type": "integer"},
                            "scheme_code": {"type": "string", "default": ""},
                            "expired": {"type": "integer", "default": None},
                            "retry_transaction_id": {"type": "string", 'default': ''},
                            "product_request_id": {"type": "string", "default": ""},
                            "contract_signed_with": {"type": "string", "default": "", "enum": [None, "urbox", "jomo"]},
                            "effectiveDate": {"type": "integer", "default": None},
                        },
                    },
                    "required": ["product_id", "quantity"],
                },
                "request_id": {"type": "string"},
                "supplier_id": {"type": "integer"},
                "po": {"type": "string"},
                "extra_info": {"type": "object"}
            },
            "required": ["products", "supplier_id", "request_id", "po"],
        }
    )
    def urcard_order(cls, args, **kwargs):
        code = CodeDao()
        response = code.urcard_order_code(**kwargs)
        return response

    @classmethod
    @validate_func(
        **{
            "type": "object",
            "properties": {
                "transaction_id": {"type": "string"}
            },
            "required": ["transaction_id"],
        }
    )
    def retry_get_code(cls, args, **kwargs):
        code = CodeDao()
        response = code.retry_get_code(**kwargs)
        return response

    @classmethod
    @validate_func(
        **{
            "type": "object",
            "properties": {
                "products": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "product_id": {"type": "integer"},
                            "quantity": {"type": "integer"},
                            "scheme_code": {"type": "string", "default": ""},
                            "expired": {"type": "integer", "default": None},
                            "retry_transaction_id": {"type": "string", 'default': ''},
                            "product_request_id": {"type": "string", "default": ""},
                            "effectiveDate": {"type": "integer", "default": None},
                        },
                    },
                    "required": ["product_id", "quantity"],
                },
                "request_id": {"type": "string"}

            },
            "required": ["products", "request_id"],
        }
    )
    def urcard_retry_order(cls, args, **kwargs):
        code = CodeDao()
        response = code.urcard_retry_order_code(**kwargs)
        return response
