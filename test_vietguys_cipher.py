#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script cho VietGuysCipher AES-256-CBC implementation
"""

import os
import sys
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding as padding_v2
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from Crypto import Random
from base64 import b64encode
import hashlib

# Mock logger để tránh import app
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def exception(self, msg): print(f"ERROR: {msg}")

logger = MockLogger()

# Copy VietGuysCipher class để test độc lập
class VietGuysCipher:
    @staticmethod
    def share_key_v4(private_key_pem, public_key_pem):
        """
        Tạo shared key từ private key và public key sử dụng ECDH và SHA256
        """
        private_key = serialization.load_pem_private_key(private_key_pem, password=None)
        public_key = serialization.load_pem_public_key(public_key_pem)
        shared_key = private_key.exchange(ec.ECDH(), public_key)
        digest = hashes.Hash(hashes.SHA256(), backend=default_backend())
        digest.update(shared_key)
        return digest.finalize()

    @staticmethod
    def encrypt(plaintext, private_key_pem, public_key_pem):
        """
        Mã hóa dữ liệu sử dụng AES-256-CBC với IV ngẫu nhiên
        """
        try:
            # Tạo shared key từ private và public key
            shared_key = VietGuysCipher.share_key_v4(private_key_pem, public_key_pem)

            # Tạo IV ngẫu nhiên 16 bytes (128 bits)
            iv = Random.new().read(16)

            # Tạo cipher với AES-256-CBC
            cipher = Cipher(algorithms.AES(shared_key), modes.CBC(iv), backend=default_backend())

            # Padding dữ liệu theo PKCS7
            padder = padding_v2.PKCS7(algorithms.AES.block_size).padder()
            padded_plaintext = padder.update(plaintext.encode('utf-8')) + padder.finalize()

            # Mã hóa
            encryptor = cipher.encryptor()
            ciphertext = encryptor.update(padded_plaintext) + encryptor.finalize()

            return {
                'ciphertext': ciphertext.hex(),
                'iv': iv.hex(),
                'combined': (iv + ciphertext).hex()
            }

        except Exception as e:
            logger.exception(f"Encryption error: {e}")
            raise e

    @staticmethod
    def decrypt(ciphertext_data, private_key_pem, public_key_pem, iv=None):
        """
        Giải mã dữ liệu đã được mã hóa bằng AES-256-CBC
        """
        try:
            # Tạo shared key từ private và public key
            shared_key = VietGuysCipher.share_key_v4(private_key_pem, public_key_pem)

            # Chuyển đổi hex thành bytes
            binary_data = bytes.fromhex(ciphertext_data)

            if iv is None:
                # IV được kết hợp với ciphertext (16 bytes đầu)
                iv_bytes = binary_data[:16]
                ciphertext_bytes = binary_data[16:]
            else:
                # IV được cung cấp riêng
                iv_bytes = bytes.fromhex(iv)
                ciphertext_bytes = binary_data

            # Tạo cipher với AES-256-CBC
            cipher = Cipher(algorithms.AES(shared_key), modes.CBC(iv_bytes), backend=default_backend())

            # Giải mã
            decryptor = cipher.decryptor()
            decrypted_padded_data = decryptor.update(ciphertext_bytes) + decryptor.finalize()

            # Loại bỏ padding
            unpadder = padding_v2.PKCS7(algorithms.AES.block_size).unpadder()
            decrypted_data = unpadder.update(decrypted_padded_data) + unpadder.finalize()

            return decrypted_data.decode('utf-8')

        except Exception as e:
            logger.exception(f"Decryption error: {e}")
            raise e


def generate_test_keys():
    """Tạo cặp private/public key để test"""
    # Tạo private key
    private_key = ec.generate_private_key(ec.SECP256R1(), default_backend())
    
    # Tạo public key từ private key
    public_key = private_key.public_key()
    
    # Serialize keys thành PEM format
    private_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    public_pem = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )
    
    return private_pem, public_pem


def test_share_key_v4():
    """Test function share_key_v4"""
    print("=== Test share_key_v4 ===")
    
    private_pem, public_pem = generate_test_keys()
    
    # Test tạo shared key
    shared_key = VietGuysCipher.share_key_v4(private_pem, public_pem)
    
    print(f"Private key length: {len(private_pem)} bytes")
    print(f"Public key length: {len(public_pem)} bytes")
    print(f"Shared key length: {len(shared_key)} bytes")
    print(f"Shared key (hex): {shared_key.hex()}")
    
    # Kiểm tra shared key có đúng 32 bytes (256 bits) không
    assert len(shared_key) == 32, f"Shared key phải có 32 bytes, nhưng có {len(shared_key)} bytes"
    
    print("✅ share_key_v4 test passed!")
    return private_pem, public_pem


def test_encrypt_decrypt():
    """Test functions encrypt và decrypt"""
    print("\n=== Test encrypt/decrypt ===")
    
    private_pem, public_pem = generate_test_keys()
    
    # Test data
    test_messages = [
        "Hello, VietGuys!",
        "Đây là tin nhắn tiếng Việt có dấu",
        "Special chars: !@#$%^&*()_+-=[]{}|;:,.<>?",
        "Long message: " + "A" * 1000,
        ""  # Empty string
    ]
    
    for i, plaintext in enumerate(test_messages):
        print(f"\nTest case {i+1}: '{plaintext[:50]}{'...' if len(plaintext) > 50 else ''}'")
        
        # Mã hóa
        encrypted_result = VietGuysCipher.encrypt(plaintext, private_pem, public_pem)
        
        print(f"Ciphertext length: {len(encrypted_result['ciphertext'])} chars")
        print(f"IV length: {len(encrypted_result['iv'])} chars")
        print(f"Combined length: {len(encrypted_result['combined'])} chars")
        
        # Kiểm tra IV có đúng 32 chars (16 bytes hex) không
        assert len(encrypted_result['iv']) == 32, f"IV hex phải có 32 chars, nhưng có {len(encrypted_result['iv'])}"
        
        # Kiểm tra combined = IV + ciphertext
        expected_combined = encrypted_result['iv'] + encrypted_result['ciphertext']
        assert encrypted_result['combined'] == expected_combined, "Combined data không đúng"
        
        # Giải mã sử dụng combined data
        decrypted_text1 = VietGuysCipher.decrypt(
            encrypted_result['combined'], 
            private_pem, 
            public_pem
        )
        
        # Giải mã sử dụng IV và ciphertext riêng biệt
        decrypted_text2 = VietGuysCipher.decrypt(
            encrypted_result['ciphertext'], 
            private_pem, 
            public_pem,
            iv=encrypted_result['iv']
        )
        
        # Kiểm tra kết quả giải mã
        assert decrypted_text1 == plaintext, f"Decryption 1 failed: expected '{plaintext}', got '{decrypted_text1}'"
        assert decrypted_text2 == plaintext, f"Decryption 2 failed: expected '{plaintext}', got '{decrypted_text2}'"
        
        print(f"✅ Test case {i+1} passed!")
    
    print("✅ All encrypt/decrypt tests passed!")


def test_iv_randomness():
    """Test tính ngẫu nhiên của IV"""
    print("\n=== Test IV randomness ===")
    
    private_pem, public_pem = generate_test_keys()
    plaintext = "Test message for IV randomness"
    
    # Mã hóa cùng một message nhiều lần
    ivs = []
    ciphertexts = []
    
    for i in range(10):
        result = VietGuysCipher.encrypt(plaintext, private_pem, public_pem)
        ivs.append(result['iv'])
        ciphertexts.append(result['ciphertext'])
    
    # Kiểm tra tất cả IV đều khác nhau
    unique_ivs = set(ivs)
    assert len(unique_ivs) == len(ivs), f"IVs không unique: {len(unique_ivs)} unique trong {len(ivs)} IVs"
    
    # Kiểm tra tất cả ciphertext đều khác nhau (do IV khác nhau)
    unique_ciphertexts = set(ciphertexts)
    assert len(unique_ciphertexts) == len(ciphertexts), f"Ciphertexts không unique: {len(unique_ciphertexts)} unique trong {len(ciphertexts)} ciphertexts"
    
    print(f"✅ Generated {len(ivs)} unique IVs and ciphertexts")
    print("✅ IV randomness test passed!")


def test_error_handling():
    """Test xử lý lỗi"""
    print("\n=== Test error handling ===")
    
    private_pem, public_pem = generate_test_keys()
    
    # Test với invalid ciphertext
    try:
        VietGuysCipher.decrypt("invalid_hex_data", private_pem, public_pem)
        assert False, "Should have raised exception for invalid hex data"
    except Exception as e:
        print(f"✅ Correctly caught exception for invalid hex: {type(e).__name__}")
    
    # Test với ciphertext quá ngắn
    try:
        VietGuysCipher.decrypt("abcd", private_pem, public_pem)
        assert False, "Should have raised exception for short ciphertext"
    except Exception as e:
        print(f"✅ Correctly caught exception for short ciphertext: {type(e).__name__}")
    
    print("✅ Error handling tests passed!")


def main():
    """Chạy tất cả tests"""
    print("🚀 Starting VietGuysCipher AES-256-CBC tests...\n")
    
    try:
        test_share_key_v4()
        test_encrypt_decrypt()
        test_iv_randomness()
        test_error_handling()
        
        print("\n🎉 All tests passed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
