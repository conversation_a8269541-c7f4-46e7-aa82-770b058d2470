#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json
from app.extensions import redis_client
from app.helper import Helper
from app.services.telegram_service import REDIS_KEY_STORE_FLAG, REDIS_KEY_STORE_MESSAGE, REDIS_KEY_STORE_QUANTITY, \
    REDIS_KEY_STORE_MESSAGE_ID
from app.telegram_bot import build_message_get_code_order_template, del_telegram_message, send_telegram_message_to_group
from celery_app import FlaskCeleryTask



class SendTelegramMessageTask(FlaskCeleryTask):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.flag_name = REDIS_KEY_STORE_FLAG

    def start_task(self):
        start_time = Helper.get_now_unix_timestamp()

        if not self.is_health():
            return

        if self.is_time_break(start_time):
            return

        self.process_telegram_queue_message()
        return

    def process_telegram_queue_message(self):
        cursor = 0
        while True:
            cursor, partial_keys = redis_client.scan(cursor=cursor, match=f'{REDIS_KEY_STORE_MESSAGE}:*', count=100)
            for key in partial_keys:
                self.process_item(key.decode('utf-8'))
            if cursor == 0:  # Đã quét hết
                break

    def process_item(self, key):
        message_key = "{key}".format(key=key)
        messages = redis_client.get(message_key)
        if not messages:
            return
        redis_client.delete(message_key)
        message = json.loads(messages.decode('utf-8'))
        order_id = message['order_id']
        total_code_receiver = redis_client.get('{key}:{order_id}'.format(key=REDIS_KEY_STORE_QUANTITY, order_id=order_id))
        if not total_code_receiver:
            total_code_receiver = 0
        else:
            total_code_receiver = int(total_code_receiver.decode('utf-8'))
        message['icon'] = "🟢" if message['order_quantity'] <= total_code_receiver else "⚪"
        message['order_quantity_success'] = total_code_receiver
        message['order_id'] = "{} - {}".format(message['order_id'],message['po'])
        telegram_message = build_message_get_code_order_template(message)
        redis_key_message_id = f'{REDIS_KEY_STORE_MESSAGE_ID}:{order_id}'
        old_message_id_bytes = redis_client.get(redis_key_message_id)

        if old_message_id_bytes:
            old_message_id = int(old_message_id_bytes.decode('utf-8'))
            del_telegram_message(old_message_id)
        send_response = send_telegram_message_to_group(telegram_message)
        if send_response and send_response.message_id and send_response.message_id not in [None, ""]:
            new_message_id = send_response.message_id
            redis_client.set(redis_key_message_id, str(new_message_id), 3600 * 2)
