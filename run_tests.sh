#!/bin/bash

# Start Redis service
echo "Starting Redis service..."
docker-compose up -d redis

# Wait for Redis to be ready
echo "Waiting for Redis to be ready..."
sleep 3

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment and install dependencies
echo "Installing dependencies..."
source venv/bin/activate
pip install pytest redis requests

# Run tests with Redis host from Docker
echo "Running tests..."
REDIS_HOST=localhost REDIS_PORT=6379 REDIS_DB=0 python -m pytest tests/app/test_utils.py -v

# Deactivate virtual environment
deactivate

# Optional: Stop Redis service
# echo "Stopping Redis service..."
# docker-compose down redis
