#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Demo script cho VietGuysCipher AES-256-CBC implementation
Minh họa cách sử dụng thực tế
"""

import os
import sys
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.backends import default_backend

# Thêm app vào Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

# Import từ test file để tránh dependency issues
from test_vietguys_cipher import VietGuysCipher, generate_test_keys


def demo_basic_usage():
    """Demo cách sử dụng cơ bản"""
    print("=== Demo Cách Sử Dụng Cơ Bản ===")
    
    # Tạo cặp key để demo
    private_pem, public_pem = generate_test_keys()
    
    # D<PERSON> liệu cần mã hóa
    plaintext = "Thông tin bí mật của VietGuys!"
    print(f"Dữ liệu gốc: {plaintext}")
    
    # Mã hóa
    encrypted_result = VietGuysCipher.encrypt(plaintext, private_pem, public_pem)
    print(f"\nKết quả mã hóa:")
    print(f"- Ciphertext: {encrypted_result['ciphertext']}")
    print(f"- IV: {encrypted_result['iv']}")
    print(f"- Combined (IV + Ciphertext): {encrypted_result['combined']}")
    
    # Giải mã sử dụng combined data
    decrypted_text = VietGuysCipher.decrypt(
        encrypted_result['combined'], 
        private_pem, 
        public_pem
    )
    print(f"\nDữ liệu sau giải mã: {decrypted_text}")
    print(f"Giải mã thành công: {decrypted_text == plaintext}")


def demo_multiple_encryptions():
    """Demo mã hóa nhiều lần cùng một dữ liệu"""
    print("\n=== Demo Mã Hóa Nhiều Lần ===")
    
    private_pem, public_pem = generate_test_keys()
    plaintext = "Cùng một tin nhắn"
    
    print(f"Dữ liệu gốc: {plaintext}")
    print("\nMã hóa cùng một dữ liệu 3 lần:")
    
    for i in range(3):
        result = VietGuysCipher.encrypt(plaintext, private_pem, public_pem)
        print(f"\nLần {i+1}:")
        print(f"  IV: {result['iv']}")
        print(f"  Ciphertext: {result['ciphertext']}")
        
        # Kiểm tra giải mã
        decrypted = VietGuysCipher.decrypt(result['combined'], private_pem, public_pem)
        print(f"  Giải mã OK: {decrypted == plaintext}")


def demo_different_data_types():
    """Demo với các loại dữ liệu khác nhau"""
    print("\n=== Demo Với Các Loại Dữ Liệu Khác Nhau ===")
    
    private_pem, public_pem = generate_test_keys()
    
    test_data = [
        ("Tiếng Việt có dấu", "Xin chào! Đây là tin nhắn tiếng Việt có dấu."),
        ("JSON data", '{"user": "vietguys", "message": "Hello World!", "timestamp": 1234567890}'),
        ("Special characters", "!@#$%^&*()_+-=[]{}|;:,.<>?/~`"),
        ("Numbers", "1234567890.123456789"),
        ("Long text", "A" * 500 + " - End of long text"),
        ("Empty string", ""),
        ("Unicode", "🚀 VietGuys Cipher 🔐 AES-256-CBC 🛡️")
    ]
    
    for data_type, data in test_data:
        print(f"\n{data_type}:")
        print(f"  Input: {data[:50]}{'...' if len(data) > 50 else ''}")
        
        # Mã hóa
        result = VietGuysCipher.encrypt(data, private_pem, public_pem)
        
        # Giải mã
        decrypted = VietGuysCipher.decrypt(result['combined'], private_pem, public_pem)
        
        print(f"  Encrypted length: {len(result['combined'])} chars")
        print(f"  Decryption success: {decrypted == data}")


def demo_separate_iv_usage():
    """Demo sử dụng IV tách riêng"""
    print("\n=== Demo Sử Dụng IV Tách Riêng ===")
    
    private_pem, public_pem = generate_test_keys()
    plaintext = "Demo IV tách riêng"
    
    print(f"Dữ liệu gốc: {plaintext}")
    
    # Mã hóa
    result = VietGuysCipher.encrypt(plaintext, private_pem, public_pem)
    print(f"\nMã hóa:")
    print(f"  Ciphertext: {result['ciphertext']}")
    print(f"  IV: {result['iv']}")
    
    # Giải mã sử dụng IV tách riêng
    decrypted = VietGuysCipher.decrypt(
        result['ciphertext'], 
        private_pem, 
        public_pem,
        iv=result['iv']
    )
    print(f"\nGiải mã với IV tách riêng: {decrypted}")
    print(f"Kết quả đúng: {decrypted == plaintext}")


def demo_key_sharing_scenario():
    """Demo kịch bản chia sẻ key thực tế"""
    print("\n=== Demo Kịch Bản Chia Sẻ Key Thực Tế ===")
    
    # Tạo 2 cặp key cho Alice và Bob
    alice_private, alice_public = generate_test_keys()
    bob_private, bob_public = generate_test_keys()
    
    print("Alice và Bob trao đổi public key với nhau")
    
    # Alice mã hóa tin nhắn gửi cho Bob
    alice_message = "Tin nhắn bí mật từ Alice gửi Bob"
    print(f"\nAlice muốn gửi: {alice_message}")
    
    # Alice sử dụng private key của mình và public key của Bob
    encrypted_by_alice = VietGuysCipher.encrypt(alice_message, alice_private, bob_public)
    print(f"Alice mã hóa với (Alice private + Bob public)")
    print(f"Ciphertext: {encrypted_by_alice['combined']}")
    
    # Bob giải mã tin nhắn từ Alice
    # Bob sử dụng private key của mình và public key của Alice
    decrypted_by_bob = VietGuysCipher.decrypt(
        encrypted_by_alice['combined'], 
        bob_private, 
        alice_public
    )
    print(f"\nBob giải mã với (Bob private + Alice public): {decrypted_by_bob}")
    print(f"Giải mã thành công: {decrypted_by_bob == alice_message}")
    
    # Bob trả lời Alice
    bob_message = "Tin nhắn phản hồi từ Bob gửi Alice"
    print(f"\nBob muốn trả lời: {bob_message}")
    
    encrypted_by_bob = VietGuysCipher.encrypt(bob_message, bob_private, alice_public)
    print(f"Bob mã hóa với (Bob private + Alice public)")
    
    decrypted_by_alice = VietGuysCipher.decrypt(
        encrypted_by_bob['combined'], 
        alice_private, 
        bob_public
    )
    print(f"Alice giải mã với (Alice private + Bob public): {decrypted_by_alice}")
    print(f"Giải mã thành công: {decrypted_by_alice == bob_message}")


def main():
    """Chạy tất cả demos"""
    print("🚀 VietGuysCipher AES-256-CBC Demo\n")
    
    demo_basic_usage()
    demo_multiple_encryptions()
    demo_different_data_types()
    demo_separate_iv_usage()
    demo_key_sharing_scenario()
    
    print("\n🎉 Demo hoàn thành!")
    print("\n📝 Lưu ý:")
    print("- Mỗi lần mã hóa sẽ tạo ra IV ngẫu nhiên mới")
    print("- Cùng một dữ liệu sẽ có ciphertext khác nhau mỗi lần mã hóa")
    print("- IV phải được lưu trữ cùng với ciphertext để giải mã")
    print("- Shared key được tạo từ ECDH + SHA256 đảm bảo tính bảo mật")


if __name__ == "__main__":
    main()
